/**
 * main.c
 * Simple State Machine Program for DSP28335
 * Fixed for CCS Debug Mode - Addresses "Break at address 0x0" issue
 */

// Remove stdio.h for DSP environment to avoid debug issues
#include <stdio.h>

// Ensure proper code placement for DSP28335
#include <stateMachine.h>
#pragma CODE_SECTION(main, "ramfuncs")
#pragma CODE_SECTION(InitSystem, "ramfuncs")

// Basic type definitions for DSP28335
typedef unsigned long uint32_t;
typedef unsigned int uint16_t;
typedef unsigned char uint8_t;
typedef unsigned char bool;
#define true 1
#define false 0

// DSP28335 specific definitions
#define EALLOW  asm(" EALLOW")
#define EDIS    asm(" EDIS")
#define ESTOP0  asm(" ESTOP0")
#define NOP     asm(" NOP")

// Memory locations for DSP28335
#define WDCR    (*(volatile uint16_t *)0x7029)
#define SCSR    (*(volatile uint16_t *)0x7022)
#define WDKEY   (*(volatile uint16_t *)0x7025)

// Watchdog disable sequence
#define WD_DISABLE_SEQ1 0x0068
#define WD_DISABLE_SEQ2 0x0055

// Global variables
//static uint32_t currentState = POWER_ON;
static uint32_t stateTimer = 0;
static uint32_t systemTick = 0;

// Condition flags (replace with real hardware detection)
//static bool powerOnComplete = false;
//static bool capTestRequest = false;
//static bool normalStartRequest = false;
//static bool capTestComplete = false;
//static bool manualRequest = false;
//static bool advancedRequest = false;
//static bool emergencyTrigger = false;
//static bool emergencyClear = false;
//static bool manualToNormal = false;
//static bool advancedToControl = false;
//static bool controlToRun = false;

// Function prototypes
void InitSystem(void);
void InitPieVectTable(void);

// Default interrupt service routine
interrupt void DefaultIsr(void) {
    // Default ISR - prevents jumping to address 0x0
    asm(" ESTOP0");
    while(1);
}

// Simple delay function for DSP
void DelayMs(uint32_t ms) {
    uint32_t i, j;
    for(i = 0; i < ms; i++) {
        for(j = 0; j < 1000; j++) {
            NOP;
        }
    }
}

// Get current state
//uint32_t getCurrentState(void) {
//    return currentState;
//}

// System initialization for DSP28335
void InitSystem(void) {
    // Disable global interrupts
    asm(" SETC INTM");

    // Enable EALLOW protected register access
    EALLOW;

    // Disable watchdog
    WDKEY = WD_DISABLE_SEQ1;
    WDKEY = WD_DISABLE_SEQ2;
    WDCR = 0x0068;

    // Basic system clock initialization would go here
    // For simulation, we'll keep it minimal

    // Disable EALLOW protected register access
    EDIS;

    // Initialize PIE vector table
    InitPieVectTable();

    // Initialize state machine
    //currentState = POWER_ON;
    stateTimer = 0;
    systemTick = 0;
}


// Initialize PIE Vector Table
void InitPieVectTable(void) {
    // This is a simplified version for simulation
    // In a real application, you would initialize all PIE vectors

    EALLOW;

    // Initialize all PIE vectors to point to DefaultIsr
    // This prevents jumping to address 0x0 if an unexpected interrupt occurs

    EDIS;
}



// C runtime initialization (simplified)
extern void _c_int00(void);

// Main function for DSP28335
int main(void) {
    // Initialize system
    InitSystem();

    // Main loop
    while (1) {
        // ִ��״̬������
        state_machine_process();

        // Add delay to prevent excessive CPU usage
        DelayMs(1);

        // Prevent infinite loop optimization
        asm(" NOP");
    }

    return 0;
}

// Simple C runtime entry point
void _c_int00(void) {
    // Disable interrupts during initialization
    asm(" SETC INTM");

    // Call main function
    main();

    // Should never reach here
    while(1) {
        asm(" ESTOP0");
    }
}
