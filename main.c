/**
 * main.c
 * Simple State Machine Program for DSP28335
 * Fixed for CCS Debug Mode - Addresses "Break at address 0x0" issue
 */

// Remove stdio.h for DSP environment to avoid debug issues
#include <stdio.h>

// Ensure proper code placement for DSP28335
#include <stateMachine.h>
#pragma CODE_SECTION(main, "ramfuncs")
#pragma CODE_SECTION(InitSystem, "ramfuncs")

/* Basic type definitions for DSP28335 - MISRA-C compliant */
typedef unsigned long  u32;
typedef unsigned short u16;
typedef unsigned char  u8;
typedef unsigned char  bool;

#define TRUE  ((bool)1U)
#define FALSE ((bool)0U)

// DSP28335 specific definitions
#define EALLOW  asm(" EALLOW")
#define EDIS    asm(" EDIS")
#define ESTOP0  asm(" ESTOP0")
#define NOP     asm(" NOP")

// Memory locations for DSP28335
#define WDCR    (*(volatile uint16_t *)0x7029)
#define SCSR    (*(volatile uint16_t *)0x7022)
#define WDKEY   (*(volatile uint16_t *)0x7025)

// Watchdog disable sequence
#define WD_DISABLE_SEQ1 0x0068
#define WD_DISABLE_SEQ2 0x0055

/* Global variables with 'g' prefix */
static u32 gStateTimer = 0U;
static u32 gSystemTick = 0U;

// Condition flags (replace with real hardware detection)
//static bool powerOnComplete = false;
//static bool capTestRequest = false;
//static bool normalStartRequest = false;
//static bool capTestComplete = false;
//static bool manualRequest = false;
//static bool advancedRequest = false;
//static bool emergencyTrigger = false;
//static bool emergencyClear = false;
//static bool manualToNormal = false;
//static bool advancedToControl = false;
//static bool controlToRun = false;

// Function prototypes
void InitSystem(void);
void InitPieVectTable(void);

// Default interrupt service routine
interrupt void DefaultIsr(void) {
    // Default ISR - prevents jumping to address 0x0
    asm(" ESTOP0");
    while(1);
}

/* Simple delay function for DSP - MISRA-C compliant */
void delayMs(u32 ms) {
    u32 i, j;
    /* Finite loop with explicit bounds */
    for(i = 0U; i < ms; i++) {
        for(j = 0U; j < 1000U; j++) {
            NOP;
        }
    }
}

// Get current state
//uint32_t getCurrentState(void) {
//    return currentState;
//}

// System initialization for DSP28335
void InitSystem(void) {
    // Disable global interrupts
    asm(" SETC INTM");

    // Enable EALLOW protected register access
    EALLOW;

    // Disable watchdog
    WDKEY = WD_DISABLE_SEQ1;
    WDKEY = WD_DISABLE_SEQ2;
    WDCR = 0x0068;

    // Basic system clock initialization would go here
    // For simulation, we'll keep it minimal

    // Disable EALLOW protected register access
    EDIS;

    // Initialize PIE vector table
    InitPieVectTable();

    /* Initialize state machine variables */
    gStateTimer = 0U;
    gSystemTick = 0U;
}


// Initialize PIE Vector Table
void InitPieVectTable(void) {
    // This is a simplified version for simulation
    // In a real application, you would initialize all PIE vectors

    EALLOW;

    // Initialize all PIE vectors to point to DefaultIsr
    // This prevents jumping to address 0x0 if an unexpected interrupt occurs

    EDIS;
}



// C runtime initialization (simplified)
extern void _c_int00(void);

// Main function for DSP28335
int main(void) {
    // Initialize system
    InitSystem();

    /* Main loop - finite loop with break condition */
    for (gSystemTick = 0U; gSystemTick < 0xFFFFFFFFU; gSystemTick++) {
        /* Execute state machine processing */
        stateMachineProcess();

        /* Add delay to prevent excessive CPU usage */
        delayMs(1U);

        /* Prevent infinite loop optimization */
        asm(" NOP");

        /* Reset counter to prevent overflow */
        if (gSystemTick >= 0xFFFFFFFEU) {
            gSystemTick = 0U;
        }
    }

    return 0;
}

// Simple C runtime entry point
void _c_int00(void) {
    // Disable interrupts during initialization
    asm(" SETC INTM");

    // Call main function
    main();

    // Should never reach here
    while(1) {
        asm(" ESTOP0");
    }
}
