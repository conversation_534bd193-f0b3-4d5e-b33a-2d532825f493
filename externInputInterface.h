#include <stdio.h>

// External input interface functions for new state machine
void input_tf1_8(void);   // PowerOn->Manual
void input_tf1_3(void);   // PowerOn->Standby
void input_tf3_6(void);   // Standby->PreNormal
void input_tf6_4(void);   // PreNormal->Normal
void input_tf6_5(void);   // PreNormal->VortexRun
void input_tf9_10(void);  // VortexRun->VortexBrake
void input_tf10_5(void);  // VortexBrake->Emergency
void input_tf4_5(void);   // Normal->Emergency
void input_tf5_7(void);   // Emergency->BackupTest
void input_tf7_3(void);   // BackupTest->Standby
void input_tf8_3(void);   // Manual->Standby
void input_tf5_3(void);   // Emergency->Standby
void input_tf9_5(void);   // VortexRun->Emergency
