/**
 * @file externInputInterface.h
 * @brief External input interface for state machine transitions
 * @note Compliant with MISRA-C:2004 standard
 */

#ifndef EXTERN_INPUT_INTERFACE_H
#define EXTERN_INPUT_INTERFACE_H

#include <stateMachine.h>

/* External input interface functions - camelCase naming */
void inputTf18(void);   /* PowerOn->Manual */
void inputTf13(void);   /* PowerOn->Standby */
void inputTf36(void);   /* Standby->PreNormal */
void inputTf64(void);   /* PreNormal->Normal */
void inputTf65(void);   /* PreNormal->VortexRun */
void inputTf910(void);  /* VortexRun->VortexBrake */
void inputTf105(void);  /* VortexBrake->Emergency */
void inputTf45(void);   /* Normal->Emergency */
void inputTf57(void);   /* Emergency->BackupTest */
void inputTf73(void);   /* BackupTest->Standby */
void inputTf83(void);   /* Manual->Standby */
void inputTf53(void);   /* Emergency->Standby */
void inputTf95(void);   /* VortexRun->Emergency */

#endif /* EXTERN_INPUT_INTERFACE_H */
