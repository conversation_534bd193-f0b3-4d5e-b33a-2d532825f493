################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
EXE_SRCS += \
../test_state_machine.exe \
../test_state_machine_updated.exe 

CMD_SRCS += \
../28335_RAM_lnk.cmd 

ASM_SRCS += \
../CodeStartBranch.asm 

C_SRCS += \
../externInputInterface.c \
../main.c \
../stateMachine.c \
../test_state_machine.c 

C_DEPS += \
./externInputInterface.d \
./main.d \
./stateMachine.d \
./test_state_machine.d 

OBJS += \
./CodeStartBranch.obj \
./externInputInterface.obj \
./main.obj \
./stateMachine.obj \
./test_state_machine.obj 

ASM_DEPS += \
./CodeStartBranch.d 

OBJS__QUOTED += \
"CodeStartBranch.obj" \
"externInputInterface.obj" \
"main.obj" \
"stateMachine.obj" \
"test_state_machine.obj" 

C_DEPS__QUOTED += \
"externInputInterface.d" \
"main.d" \
"stateMachine.d" \
"test_state_machine.d" 

ASM_DEPS__QUOTED += \
"CodeStartBranch.d" 

ASM_SRCS__QUOTED += \
"../CodeStartBranch.asm" 

C_SRCS__QUOTED += \
"../externInputInterface.c" \
"../main.c" \
"../stateMachine.c" \
"../test_state_machine.c" 

EXE_SRCS__QUOTED += \
"../test_state_machine.exe" \
"../test_state_machine_updated.exe" 


