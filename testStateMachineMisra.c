/**
 * @file testStateMachineMisra.c
 * @brief Test program for MISRA-C compliant state machine
 * @note Compliant with MISRA-C:2004 standard
 */

#include <stdio.h>
#include <stateMachine.h>
#include <externInputInterface.h>

/* Test function prototypes */
static void testStateMachineTransitions(void);
static void testAlternativePath(void);
static void displayStateCounters(void);
static void resetAllConditions(void);

/* Test function to simulate state transitions */
static void testStateMachineTransitions(void) {
    printf("=== MISRA-C Compliant State Machine Test ===\n");

    /* Reset to initial state */
    gCurrentState = POWER_ON;
    gCurrentStateBak = POWER_ON;
    resetAllConditions();
    printf("Initial state: %u (POWER_ON)\n", (u32)getCurrentState());

    /* Test 1: PowerOn -> Standby (TF1_3) */
    printf("\n--- Test 1: PowerOn -> Standby ---\n");
    inputTf13();  /* Simulate power-on complete */
    stateMachineProcess();
    printf("After TF1_3: Current state = %u (Expected: %u STANDBY)\n",
           (u32)getCurrentState(), (u32)STANDBY);
    resetAllConditions();

    /* Test 2: Standby -> PreNormal (TF3_6) */
    printf("\n--- Test 2: Standby -> PreNormal ---\n");
    inputTf36();  /* Simulate start operation request */
    stateMachineProcess();
    printf("After TF3_6: Current state = %u (Expected: %u PRE_NORMAL)\n",
           (u32)getCurrentState(), (u32)PRE_NORMAL);
    resetAllConditions();

    /* Test 3: PreNormal -> VortexRun (TF6_5) */
    printf("\n--- Test 3: PreNormal -> VortexRun ---\n");
    inputTf65();  /* Simulate vortex mode selection */
    stateMachineProcess();
    printf("After TF6_5: Current state = %u (Expected: %u VORTEX_RUN)\n",
           (u32)getCurrentState(), (u32)VORTEX_RUN);
    resetAllConditions();

    /* Test 4: VortexRun -> VortexBrake (TF9_10) */
    printf("\n--- Test 4: VortexRun -> VortexBrake ---\n");
    inputTf910(); /* Simulate brake mode trigger */
    stateMachineProcess();
    printf("After TF9_10: Current state = %u (Expected: %u VORTEX_BRAKE)\n",
           (u32)getCurrentState(), (u32)VORTEX_BRAKE);
    resetAllConditions();

    /* Test 5: VortexBrake -> Emergency (TF10_5) */
    printf("\n--- Test 5: VortexBrake -> Emergency ---\n");
    inputTf105(); /* Simulate emergency detection */
    stateMachineProcess();
    printf("After TF10_5: Current state = %u (Expected: %u EMERGENCY)\n",
           (u32)getCurrentState(), (u32)EMERGENCY);
    resetAllConditions();

    /* Test 6: Emergency -> BackupTest (TF5_7) */
    printf("\n--- Test 6: Emergency -> BackupTest ---\n");
    inputTf57();  /* Simulate backup test request */
    stateMachineProcess();
    printf("After TF5_7: Current state = %u (Expected: %u BACKUP_TEST)\n",
           (u32)getCurrentState(), (u32)BACKUP_TEST);
    resetAllConditions();

    /* Test 7: BackupTest -> Standby (TF7_3) */
    printf("\n--- Test 7: BackupTest -> Standby ---\n");
    inputTf73();  /* Simulate backup test complete */
    stateMachineProcess();
    printf("After TF7_3: Current state = %u (Expected: %u STANDBY)\n",
           (u32)getCurrentState(), (u32)STANDBY);

    printf("\n=== State Machine Test Complete ===\n");
}

/* Test function to demonstrate alternative path */
static void testAlternativePath(void) {
    printf("\n=== Testing Alternative Path ===\n");
    
    /* Reset to POWER_ON */
    gCurrentState = POWER_ON;
    gCurrentStateBak = POWER_ON;
    printf("Reset to POWER_ON: %u\n", (u32)getCurrentState());
    
    /* Reset conditions */
    resetAllConditions();
    
    /* PowerOn -> Manual (TF1_8) */
    printf("\n--- Test: PowerOn -> Manual ---\n");
    inputTf18();  /* Simulate manual mode request */
    stateMachineProcess();
    printf("After TF1_8: Current state = %u (Expected: %u MANUAL)\n", 
           (u32)getCurrentState(), (u32)MANUAL);
    
    /* Reset conditions for next test */
    resetAllConditions();
    
    /* Manual -> Standby (TF8_3) */
    printf("\n--- Test: Manual -> Standby ---\n");
    inputTf83();  /* Simulate exit manual mode */
    stateMachineProcess();
    printf("After TF8_3: Current state = %u (Expected: %u STANDBY)\n", 
           (u32)getCurrentState(), (u32)STANDBY);
    
    /* Reset conditions for next test */
    resetAllConditions();
    
    /* Test PreNormal -> Normal path */
    printf("\n--- Test: Standby -> PreNormal -> Normal ---\n");
    inputTf36();  /* Standby -> PreNormal */
    stateMachineProcess();
    printf("After TF3_6: Current state = %u (PRE_NORMAL)\n", (u32)getCurrentState());
    
    /* Reset conditions for next test */
    resetAllConditions();
    
    inputTf64();  /* PreNormal -> Normal */
    stateMachineProcess();
    printf("After TF6_4: Current state = %u (Expected: %u NORMAL)\n", 
           (u32)getCurrentState(), (u32)NORMAL);
    
    /* Reset conditions for next test */
    resetAllConditions();
    
    /* Normal -> Emergency (TF4_5) */
    printf("\n--- Test: Normal -> Emergency ---\n");
    inputTf45();  /* Simulate emergency in normal mode */
    stateMachineProcess();
    printf("After TF4_5: Current state = %u (Expected: %u EMERGENCY)\n", 
           (u32)getCurrentState(), (u32)EMERGENCY);
    
    printf("\n=== Alternative Path Test Complete ===\n");
}

/* Display state counters */
static void displayStateCounters(void) {
    printf("\n=== State Execution Counters ===\n");
    printf("PowerOn: %lu\n", gPowerOnCnt);
    printf("Standby: %lu\n", gStandbyCnt);
    printf("PreNormal: %lu\n", gPreNormalCnt);
    printf("Normal: %lu\n", gNormalCnt);
    printf("Emergency: %lu\n", gEmergencyCnt);
    printf("BackupTest: %lu\n", gBackupTestCnt);
    printf("Manual: %lu\n", gManualCnt);
    printf("VortexRun: %lu\n", gVortexRunCnt);
    printf("VortexBrake: %lu\n", gVortexBrakeCnt);
    printf("================================\n");
}

/* Reset all transition conditions */
static void resetAllConditions(void) {
    gTf18Condition = 0U;
    gTf13Condition = 0U;
    gTf36Condition = 0U;
    gTf64Condition = 0U;
    gTf65Condition = 0U;
    gTf910Condition = 0U;
    gTf105Condition = 0U;
    gTf45Condition = 0U;
    gTf57Condition = 0U;
    gTf73Condition = 0U;
    gTf83Condition = 0U;
    gTf53Condition = 0U;
    gTf95Condition = 0U;
}

#ifdef TEST_MAIN
int main(void) {
    printf("MISRA-C Compliant State Machine Test Program\n");
    printf("Based on Updated Flowchart with Vortex Modes\n");
    printf("============================================\n");
    
    /* Run main test sequence */
    testStateMachineTransitions();
    
    /* Display counters after main test */
    displayStateCounters();
    
    /* Run alternative path test */
    testAlternativePath();
    
    /* Display final counters */
    displayStateCounters();
    
    return 0;
}
#endif
