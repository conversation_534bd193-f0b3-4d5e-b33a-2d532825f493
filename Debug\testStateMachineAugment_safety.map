******************************************************************************
             TMS320C2000 Linker PC v18.1.4                     
******************************************************************************
>> Linked Thu Jul 10 14:21:03 2025

OUTPUT FILE NAME:   <testStateMachineAugment_safety.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 000055b8


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  ZONE0                 00004000   00001000  00000108  00000ef8  RWIX
  ZONE1                 00005000   00001000  00000953  000006ad  RWIX
  ZONE2                 00006000   00001000  00000000  00001000  RWIX
  ZONE3                 00007000   00001000  00000000  00001000  RWIX
  ZONE4                 00008000   00001000  00000000  00001000  RWIX
  ZONE5                 00009000   00001000  00000000  00001000  RWIX
  ZONE6                 0000a000   00001000  00000000  00001000  RWIX
  ZONE7                 0000b000   00001000  00000000  00001000  RWIX
  CSM_RSVD              0033ff80   00000076  00000000  00000076  RWIX
  CSM_PWL               0033fff8   00000008  00000000  00000008  RWIX
  ADC_CAL               00380080   00000009  00000000  00000009  RWIX
  RESET                 003fffc0   00000002  00000000  00000002  RWIX
  VECTORS               003fffc2   0000003e  00000000  0000003e  RWIX

PAGE 1:
  M0SARAM               00000000   00000400  00000300  00000100  RWIX
  M1SARAM               00000400   00000400  00000120  000002e0  RWIX
  PIEDATA               00000ce0   00000020  00000000  00000020  RWIX
  L0SARAM               00008000   00001000  0000089b  00000765  RWIX
  L1SARAM               00009000   00001000  00000400  00000c00  RWIX
  L2SARAM               0000a000   00001000  00000000  00001000  RWIX
  L3SARAM               0000b000   00001000  00000000  00001000  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.cinit     0    00004000    000000e7     
                  00004000    0000005c     stateMachine.obj (.cinit)
                  0000405c    0000002a     rts2800_fpu32.lib : defs.c.obj (.cinit)
                  00004086    00000029                       : write.c.obj (.cinit)
                  000040af    0000000e                       : exit.c.obj (.cinit)
                  000040bd    0000000a     main.obj (.cinit)
                  000040c7    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  000040cc    00000005                       : _lock.c.obj (.cinit:__unlock)
                  000040d1    00000004     externInputInterface.obj (.cinit)
                  000040d5    00000004     rts2800_fpu32.lib : errno.c.obj (.cinit)
                  000040d9    00000004                       : fopen.c.obj (.cinit)
                  000040dd    00000004                       : memory.c.obj (.cinit)
                  000040e1    00000004                       : open.c.obj (.cinit)
                  000040e5    00000002     --HOLE-- [fill = 0]

.pinit     0    00004000    00000000     UNINITIALIZED

ramfuncs   0    000040e7    0000001f     
                  000040e7    0000001f     main.obj (ramfuncs)

codestart 
*          0    00004106    00000002     
                  00004106    00000002     CodeStartBranch.obj (codestart)

.text      0    00005000    00000953     
                  00005000    00000167     stateMachine.obj (.text)
                  00005167    00000107     rts2800_fpu32.lib : ll_div28.asm.obj (.text)
                  0000526e    000000a9     externInputInterface.obj (.text)
                  00005317    0000009c     rts2800_fpu32.lib : fd_add28.asm.obj (.text)
                  000053b3    00000001                       : startup.c.obj (.text)
                  000053b4    00000095                       : trgmsg.c.obj (.text)
                  00005449    0000008b                       : fd_div28.asm.obj (.text)
                  000054d4    00000083                       : fd_mpy28.asm.obj (.text)
                  00005557    00000061                       : hostlseek.c.obj (.text)
                  000055b8    00000056                       : boot28.asm.obj (.text)
                  0000560e    00000040                       : hostopen.c.obj (.text)
                  0000564e    0000003f                       : hostread.c.obj (.text)
                  0000568d    0000003e                       : hostrename.c.obj (.text)
                  000056cb    00000038                       : hostwrite.c.obj (.text)
                  00005703    0000002a                       : fd_cmp28.asm.obj (.text)
                  0000572d    0000002a                       : l_div28.asm.obj (.text)
                  00005757    00000029                       : exit.c.obj (.text)
                  00005780    00000029                       : hostclose.c.obj (.text)
                  000057a9    00000028     main.obj (.text)
                  000057d1    00000024     rts2800_fpu32.lib : cpy_tbl.c.obj (.text)
                  000057f5    00000024                       : hostunlink.c.obj (.text)
                  00005819    00000021                       : fd_toi28.asm.obj (.text)
                  0000583a    0000001f                       : fd_tol28.asm.obj (.text)
                  00005859    0000001e                       : ll_cmp28.asm.obj (.text)
                  00005877    0000001d                       : i_tofd28.asm.obj (.text)
                  00005894    0000001d                       : memcpy.c.obj (.text)
                  000058b1    0000001c                       : fs_tofdfpu32.asm.obj (.text)
                  000058cd    0000001c                       : l_tofd28.asm.obj (.text)
                  000058e9    00000019                       : args_main.c.obj (.text)
                  00005902    00000018                       : ll_aox28.asm.obj (.text)
                  0000591a    0000000e                       : fd_sub28.asm.obj (.text)
                  00005928    0000000a     main.obj (.text:retain)
                  00005932    00000009     rts2800_fpu32.lib : _lock.c.obj (.text)
                  0000593b    00000009                       : fd_neg28.asm.obj (.text)
                  00005944    00000008                       : strlen.c.obj (.text)
                  0000594c    00000005                       : strcpy.c.obj (.text)
                  00005951    00000002                       : pre_init.c.obj (.text)

.reset     0    003fffc0    00000002     DSECT
                  003fffc0    00000002     rts2800_fpu32.lib : boot28.asm.obj (.reset)

vectors    0    003fffc2    00000000     DSECT

.stack     1    00000000    00000300     UNINITIALIZED
                  00000000    00000300     --HOLE--

.econst    1    00008000    0000067a     
                  00008000    00000612     test_state_machine.obj (.econst:.string)
                  00008612    00000026     rts2800_fpu32.lib : _printfi.c.obj (.econst:.string)
                  00008638    0000001c                       : _printfi.c.obj (.econst)
                  00008654    0000001c                       : s_scalbn.c.obj (.econst)
                  00008670    00000008                       : s_frexp.c.obj (.econst)
                  00008678    00000002                       : fputs.c.obj (.econst:.string)

.ebss      1    0000867a    00000221     UNINITIALIZED
                  0000867a    00000006     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  00008680    00000160                       : defs.c.obj (.ebss)
                  000087e0    00000017     stateMachine.obj (.ebss)
                  000087f7    00000008     rts2800_fpu32.lib : hostopen.c.obj (.ebss)
                  000087ff    00000001     externInputInterface.obj (.ebss)
                  00008800    00000088     rts2800_fpu32.lib : write.c.obj (.ebss)
                  00008888    00000008                       : memory.c.obj (.ebss)
                  00008890    00000004     main.obj (.ebss)
                  00008894    00000002     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  00008896    00000002                       : _lock.c.obj (.ebss:__unlock)
                  00008898    00000001                       : errno.c.obj (.ebss)
                  00008899    00000001                       : fopen.c.obj (.ebss)
                  0000889a    00000001                       : open.c.obj (.ebss)

.esysmem   1    00009000    00000400     UNINITIALIZED
                  00009000    00000001     rts2800_fpu32.lib : memory.c.obj (.esysmem)
                  00009001    000003ff     --HOLE--

.cio       1    00000400    00000120     UNINITIALIZED
                  00000400    00000120     rts2800_fpu32.lib : trgmsg.c.obj (.cio)

MODULE SUMMARY

       Module                     code   initialized data   uninitialized data
       ------                     ----   ----------------   ------------------
    .\
       test_state_machine.obj     0      1554               0                 
       stateMachine.obj           359    92                 23                
       externInputInterface.obj   169    4                  1                 
       main.obj                   81     10                 4                 
       CodeStartBranch.obj        2      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     611    1660               28                
                                                                              
    D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\rts2800_fpu32.lib
       trgmsg.c.obj               149    0                  288               
       defs.c.obj                 0      42                 352               
       ll_div28.asm.obj           263    0                  0                 
       write.c.obj                0      41                 136               
       fd_add28.asm.obj           156    0                  0                 
       fd_div28.asm.obj           139    0                  0                 
       fd_mpy28.asm.obj           131    0                  0                 
       hostlseek.c.obj            97     0                  0                 
       boot28.asm.obj             86     0                  0                 
       hostopen.c.obj             64     0                  8                 
       _printfi.c.obj             0      66                 0                 
       hostread.c.obj             63     0                  0                 
       hostrename.c.obj           62     0                  0                 
       exit.c.obj                 41     14                 6                 
       hostwrite.c.obj            56     0                  0                 
       fd_cmp28.asm.obj           42     0                  0                 
       l_div28.asm.obj            42     0                  0                 
       hostclose.c.obj            41     0                  0                 
       cpy_tbl.c.obj              36     0                  0                 
       hostunlink.c.obj           36     0                  0                 
       fd_toi28.asm.obj           33     0                  0                 
       fd_tol28.asm.obj           31     0                  0                 
       ll_cmp28.asm.obj           30     0                  0                 
       i_tofd28.asm.obj           29     0                  0                 
       memcpy.c.obj               29     0                  0                 
       fs_tofdfpu32.asm.obj       28     0                  0                 
       l_tofd28.asm.obj           28     0                  0                 
       s_scalbn.c.obj             0      28                 0                 
       args_main.c.obj            25     0                  0                 
       ll_aox28.asm.obj           24     0                  0                 
       _lock.c.obj                9      10                 4                 
       fd_sub28.asm.obj           14     0                  0                 
       memory.c.obj               0      4                  8                 
       fd_neg28.asm.obj           9      0                  0                 
       s_frexp.c.obj              0      8                  0                 
       strlen.c.obj               8      0                  0                 
       errno.c.obj                0      4                  1                 
       fopen.c.obj                0      4                  1                 
       open.c.obj                 0      4                  1                 
       strcpy.c.obj               5      0                  0                 
       fputs.c.obj                0      2                  0                 
       pre_init.c.obj             2      0                  0                 
       startup.c.obj              1      0                  0                 
    +--+--------------------------+------+------------------+--------------------+
       Total:                     1809   227                805               
                                                                              
       Heap:                      0      0                  1024              
       Stack:                     0      0                  768               
    +--+--------------------------+------+------------------+--------------------+
       Grand Total:               2420   1887               2625              


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000000       0 (00000000)     __stack

00000400      10 (00000400)     __CIOBUF_

0000867a     219 (00008640)     ___TI_enable_exit_profile_output
0000867c     219 (00008640)     ___TI_cleanup_ptr
0000867e     219 (00008640)     ___TI_dtors_ptr

00008680     21a (00008680)     ___TI_ft_end

000086c0     21b (000086c0)     __ftable

00008740     21d (00008740)     ___TI_tmpnams

000087e0     21f (000087c0)     _TF7_3_condition
000087e1     21f (000087c0)     _TF8_3_condition
000087e2     21f (000087c0)     _TF5_3_condition
000087e3     21f (000087c0)     _powerOn
000087e4     21f (000087c0)     _TF9_5_condition
000087e5     21f (000087c0)     _TF6_5_condition
000087e6     21f (000087c0)     _TF9_10_condition
000087e7     21f (000087c0)     _TF4_5_condition
000087e8     21f (000087c0)     _TF10_5_condition
000087e9     21f (000087c0)     _manual
000087ea     21f (000087c0)     _backupTest
000087eb     21f (000087c0)     _vortexBrake
000087ec     21f (000087c0)     _vortexRun
000087ed     21f (000087c0)     _current_state
000087ee     21f (000087c0)     _standby
000087ef     21f (000087c0)     _TF5_7_condition
000087f0     21f (000087c0)     _normal
000087f1     21f (000087c0)     _preNormal
000087f2     21f (000087c0)     _emergency
000087f3     21f (000087c0)     _TF1_8_condition
000087f4     21f (000087c0)     _TF3_6_condition
000087f5     21f (000087c0)     _TF1_3_condition
000087f6     21f (000087c0)     _TF6_4_condition
000087f7     21f (000087c0)     _parmbuf
000087ff     21f (000087c0)     _DI

00008800     220 (00008800)     __stream

00008840     221 (00008840)     __device

00008894     222 (00008880)     __lock
00008896     222 (00008880)     __unlock
00008898     222 (00008880)     _errno

00009000     240 (00009000)     __sys_memory


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                            
----  -------   ----                            
0     00005000  .text                           
0     00005757  C$$EXIT                         
0     00005407  C$$IO$$                         
0     00005317  FD$$ADD                         
0     00005703  FD$$CMP                         
0     00005449  FD$$DIV                         
0     000054d4  FD$$MPY                         
0     0000593b  FD$$NEG                         
0     0000591a  FD$$SUB                         
0     00005819  FD$$TOI                         
0     0000583a  FD$$TOL                         
0     000058b1  FS$$TOFD                        
0     00005877  I$$TOFD                         
0     0000572d  L$$DIV                          
0     0000573c  L$$MOD                          
0     000058cd  L$$TOFD                         
0     00005902  LL$$AND                         
0     00005859  LL$$CMP                         
0     00005167  LL$$DIV                         
0     000051a3  LL$$MOD                         
0     0000590a  LL$$OR                          
0     00005912  LL$$XOR                         
0     0000574a  UL$$DIV                         
0     00005751  UL$$MOD                         
0     0000586b  ULL$$CMP                        
0     000051dd  ULL$$DIV                        
0     0000520c  ULL$$MOD                        
0     00005009  _CLR_BIT                        
1     000087ff  _DI                             
0     00005928  _DefaultIsr                     
0     000057a9  _DelayMs                        
0     00005780  _HOSTclose                      
0     00005557  _HOSTlseek                      
0     0000560e  _HOSTopen                       
0     0000564e  _HOSTread                       
0     0000568d  _HOSTrename                     
0     000057f5  _HOSTunlink                     
0     000056cb  _HOSTwrite                      
0     000057c7  _InitPieVectTable               
0     000040e7  _InitSystem                     
0     00005000  _SET_BIT                        
1     000087e8  _TF10_5_condition               
1     000087f5  _TF1_3_condition                
1     000087f3  _TF1_8_condition                
1     000087f4  _TF3_6_condition                
1     000087e7  _TF4_5_condition                
1     000087e2  _TF5_3_condition                
1     000087ef  _TF5_7_condition                
1     000087f6  _TF6_4_condition                
1     000087e5  _TF6_5_condition                
1     000087e0  _TF7_3_condition                
1     000087e1  _TF8_3_condition                
1     000087e6  _TF9_10_condition               
1     000087e4  _TF9_5_condition                
1     00000400  __CIOBUF_                       
1     00000300  __STACK_END                     
abs   00000300  __STACK_SIZE                    
abs   00000400  __SYSMEM_SIZE                   
1     0000867c  ___TI_cleanup_ptr               
1     0000867e  ___TI_dtors_ptr                 
1     0000867a  ___TI_enable_exit_profile_output
1     00008680  ___TI_ft_end                    
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
0     0000540c  ___TI_readmsg                   
1     00008740  ___TI_tmpnams                   
0     000053b4  ___TI_writemsg                  
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
0     00004000  ___cinit__                      
0     00005953  ___etext__                      
abs   ffffffff  ___pinit__                      
0     00005000  ___text__                       
0     000058e9  __args_main                     
0     000057cb  __c_int00                       
1     00008840  __device                        
1     000086c0  __ftable                        
1     00008894  __lock                          
0     0000593a  __nop                           
0     00005936  __register_lock                 
0     00005932  __register_unlock               
1     00000000  __stack                         
1     00008800  __stream                        
1     00009000  __sys_memory                    
0     000053b3  __system_post_cinit             
0     00005951  __system_pre_init               
1     00008896  __unlock                        
0     00005757  _abort                          
1     000087ea  _backupTest                     
0     000055b8  _c_int00                        
0     000057d1  _copy_in                        
1     000087ed  _current_state                  
1     000087f2  _emergency                      
1     00008898  _errno                          
0     00005759  _exit                           
0     00005013  _get_current_state              
0     000052bc  _input_tf10_5                   
0     0000527b  _input_tf1_3                    
0     0000526e  _input_tf1_8                    
0     00005288  _input_tf3_6                    
0     000052c9  _input_tf4_5                    
0     000052fd  _input_tf5_3                    
0     000052d6  _input_tf5_7                    
0     00005295  _input_tf6_4                    
0     000052a2  _input_tf6_5                    
0     000052e3  _input_tf7_3                    
0     000052f0  _input_tf8_3                    
0     000052af  _input_tf9_10                   
0     0000530a  _input_tf9_5                    
0     000040fc  _main                           
1     000087e9  _manual                         
0     00005894  _memcpy                         
1     000087f0  _normal                         
1     000087f7  _parmbuf                        
1     000087e3  _powerOn                        
1     000087f1  _preNormal                      
1     000087ee  _standby                        
0     000050a1  _state_machine_process          
0     0000594c  _strcpy                         
0     00005944  _strlen                         
1     000087eb  _vortexBrake                    
1     000087ec  _vortexRun                      
abs   ffffffff  binit                           
0     00004000  cinit                           
0     00004106  code_start                      
0     00005953  etext                           
abs   ffffffff  pinit                           


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                            
----  -------   ----                            
0     00004000  ___cinit__                      
0     00004000  cinit                           
0     000040e7  _InitSystem                     
0     000040fc  _main                           
0     00004106  code_start                      
0     00005000  .text                           
0     00005000  _SET_BIT                        
0     00005000  ___text__                       
0     00005009  _CLR_BIT                        
0     00005013  _get_current_state              
0     000050a1  _state_machine_process          
0     00005167  LL$$DIV                         
0     000051a3  LL$$MOD                         
0     000051dd  ULL$$DIV                        
0     0000520c  ULL$$MOD                        
0     0000526e  _input_tf1_8                    
0     0000527b  _input_tf1_3                    
0     00005288  _input_tf3_6                    
0     00005295  _input_tf6_4                    
0     000052a2  _input_tf6_5                    
0     000052af  _input_tf9_10                   
0     000052bc  _input_tf10_5                   
0     000052c9  _input_tf4_5                    
0     000052d6  _input_tf5_7                    
0     000052e3  _input_tf7_3                    
0     000052f0  _input_tf8_3                    
0     000052fd  _input_tf5_3                    
0     0000530a  _input_tf9_5                    
0     00005317  FD$$ADD                         
0     000053b3  __system_post_cinit             
0     000053b4  ___TI_writemsg                  
0     00005407  C$$IO$$                         
0     0000540c  ___TI_readmsg                   
0     00005449  FD$$DIV                         
0     000054d4  FD$$MPY                         
0     00005557  _HOSTlseek                      
0     000055b8  _c_int00                        
0     0000560e  _HOSTopen                       
0     0000564e  _HOSTread                       
0     0000568d  _HOSTrename                     
0     000056cb  _HOSTwrite                      
0     00005703  FD$$CMP                         
0     0000572d  L$$DIV                          
0     0000573c  L$$MOD                          
0     0000574a  UL$$DIV                         
0     00005751  UL$$MOD                         
0     00005757  C$$EXIT                         
0     00005757  _abort                          
0     00005759  _exit                           
0     00005780  _HOSTclose                      
0     000057a9  _DelayMs                        
0     000057c7  _InitPieVectTable               
0     000057cb  __c_int00                       
0     000057d1  _copy_in                        
0     000057f5  _HOSTunlink                     
0     00005819  FD$$TOI                         
0     0000583a  FD$$TOL                         
0     00005859  LL$$CMP                         
0     0000586b  ULL$$CMP                        
0     00005877  I$$TOFD                         
0     00005894  _memcpy                         
0     000058b1  FS$$TOFD                        
0     000058cd  L$$TOFD                         
0     000058e9  __args_main                     
0     00005902  LL$$AND                         
0     0000590a  LL$$OR                          
0     00005912  LL$$XOR                         
0     0000591a  FD$$SUB                         
0     00005928  _DefaultIsr                     
0     00005932  __register_unlock               
0     00005936  __register_lock                 
0     0000593a  __nop                           
0     0000593b  FD$$NEG                         
0     00005944  _strlen                         
0     0000594c  _strcpy                         
0     00005951  __system_pre_init               
0     00005953  ___etext__                      
0     00005953  etext                           
1     00000000  __stack                         
1     00000300  __STACK_END                     
1     00000400  __CIOBUF_                       
1     0000867a  ___TI_enable_exit_profile_output
1     0000867c  ___TI_cleanup_ptr               
1     0000867e  ___TI_dtors_ptr                 
1     00008680  ___TI_ft_end                    
1     000086c0  __ftable                        
1     00008740  ___TI_tmpnams                   
1     000087e0  _TF7_3_condition                
1     000087e1  _TF8_3_condition                
1     000087e2  _TF5_3_condition                
1     000087e3  _powerOn                        
1     000087e4  _TF9_5_condition                
1     000087e5  _TF6_5_condition                
1     000087e6  _TF9_10_condition               
1     000087e7  _TF4_5_condition                
1     000087e8  _TF10_5_condition               
1     000087e9  _manual                         
1     000087ea  _backupTest                     
1     000087eb  _vortexBrake                    
1     000087ec  _vortexRun                      
1     000087ed  _current_state                  
1     000087ee  _standby                        
1     000087ef  _TF5_7_condition                
1     000087f0  _normal                         
1     000087f1  _preNormal                      
1     000087f2  _emergency                      
1     000087f3  _TF1_8_condition                
1     000087f4  _TF3_6_condition                
1     000087f5  _TF1_3_condition                
1     000087f6  _TF6_4_condition                
1     000087f7  _parmbuf                        
1     000087ff  _DI                             
1     00008800  __stream                        
1     00008840  __device                        
1     00008894  __lock                          
1     00008896  __unlock                        
1     00008898  _errno                          
1     00009000  __sys_memory                    
abs   00000300  __STACK_SIZE                    
abs   00000400  __SYSMEM_SIZE                   
abs   ffffffff  ___TI_pprof_out_hndl            
abs   ffffffff  ___TI_prof_data_size            
abs   ffffffff  ___TI_prof_data_start           
abs   ffffffff  ___binit__                      
abs   ffffffff  ___c_args__                     
abs   ffffffff  ___pinit__                      
abs   ffffffff  binit                           
abs   ffffffff  pinit                           

[128 symbols]
