# Makefile for State Machine Test
# Updated for new flowchart implementation

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -DTEST_MAIN
INCLUDES = -I.

# Source files
SOURCES = stateMachine.c externInputInterface.c test_state_machine.c
OBJECTS = $(SOURCES:.c=.o)
TARGET = test_state_machine

# Default target
all: $(TARGET)

# Build the test executable
$(TARGET): $(OBJECTS)
	$(CC) $(OBJECTS) -o $(TARGET)

# Compile source files
%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Clean build files
clean:
	rm -f $(OBJECTS) $(TARGET)

# Run the test
test: $(TARGET)
	./$(TARGET)

# Show state machine structure
info:
	@echo "=== Updated State Machine Structure ==="
	@echo "States:"
	@echo "  1. POWER_ON (1)"
	@echo "  3. STANDBY (3)"
	@echo "  6. PRE_NORMAL (6)"
	@echo "  4. NORMAL (4)"
	@echo "  5. EMERGENCY (5)"
	@echo "  7. BACKUP_TEST (7)"
	@echo "  8. MANUAL (8)"
	@echo "  9. VORTEX_RUN (9) - 涡激运行模式"
	@echo " 10. VORTEX_BRAKE (10) - 涡激抱闸模式"
	@echo ""
	@echo "Transitions (based on flowchart):"
	@echo "  TF1_8:  PowerOn -> Manual"
	@echo "  TF1_3:  PowerOn -> Standby"
	@echo "  TF3_6:  Standby -> PreNormal"
	@echo "  TF6_4:  PreNormal -> Normal"
	@echo "  TF6_5:  PreNormal -> VortexRun"
	@echo "  TF9_10: VortexRun -> VortexBrake"
	@echo "  TF10_5: VortexBrake -> Emergency"
	@echo "  TF4_5:  Normal -> Emergency"
	@echo "  TF5_7:  Emergency -> BackupTest"
	@echo "  TF7_3:  BackupTest -> Standby"
	@echo "  TF8_3:  Manual -> Standby"
	@echo "  TF5_3:  Emergency -> Standby"
	@echo "  TF9_5:  VortexRun -> Emergency"
	@echo "======================================="

# Help target
help:
	@echo "Available targets:"
	@echo "  all     - Build the test program"
	@echo "  test    - Build and run the test program"
	@echo "  clean   - Remove build files"
	@echo "  info    - Show state machine structure"
	@echo "  help    - Show this help message"

.PHONY: all clean test info help
