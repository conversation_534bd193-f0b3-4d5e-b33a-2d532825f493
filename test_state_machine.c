/**
 * test_state_machine.c
 * Simple test program to demonstrate the updated state machine
 * Based on the new flowchart with vortex run modes
 */

#include <stdio.h>
#include <stateMachine.h>
#include <externInputInterface.h>

// Test function to simulate state transitions
void test_state_machine_transitions(void) {
    printf("=== State Machine Test Based on New Flowchart ===\n");
    printf("Initial state: %d (POWER_ON)\n", get_current_state());
    
    // Test 1: PowerOn -> Standby (TF1_3)
    printf("\n--- Test 1: PowerOn -> Standby ---\n");
    input_tf1_3();  // Simulate power-on complete
    state_machine_process();
    printf("After TF1_3: Current state = %d (Expected: %d STANDBY)\n", 
           get_current_state(), STANDBY);
    
    // Test 2: Standby -> PreNormal (TF3_6)
    printf("\n--- Test 2: Standby -> PreNormal ---\n");
    input_tf3_6();  // Simulate start operation request
    state_machine_process();
    printf("After TF3_6: Current state = %d (Expected: %d PRE_NORMAL)\n", 
           get_current_state(), PRE_NORMAL);
    
    // Test 3: PreNormal -> VortexRun (TF6_5)
    printf("\n--- Test 3: PreNormal -> VortexRun ---\n");
    input_tf6_5();  // Simulate vortex mode selection
    state_machine_process();
    printf("After TF6_5: Current state = %d (Expected: %d VORTEX_RUN)\n", 
           get_current_state(), VORTEX_RUN);
    
    // Test 4: VortexRun -> VortexBrake (TF9_10)
    printf("\n--- Test 4: VortexRun -> VortexBrake ---\n");
    input_tf9_10(); // Simulate brake mode trigger
    state_machine_process();
    printf("After TF9_10: Current state = %d (Expected: %d VORTEX_BRAKE)\n",
           get_current_state(), VORTEX_BRAKE);

    // Test 5: VortexBrake -> Emergency (TF10_5)
    printf("\n--- Test 5: VortexBrake -> Emergency ---\n");
    input_tf10_5(); // Simulate emergency detection
    state_machine_process();
    printf("After TF10_5: Current state = %d (Expected: %d EMERGENCY)\n",
           get_current_state(), EMERGENCY);
    
    // Test 6: Emergency -> BackupTest (TF5_7)
    printf("\n--- Test 6: Emergency -> BackupTest ---\n");
    input_tf5_7();  // Simulate backup test request
    state_machine_process();
    printf("After TF5_7: Current state = %d (Expected: %d BACKUP_TEST)\n", 
           get_current_state(), BACKUP_TEST);
    
    // Test 7: BackupTest -> Standby (TF7_3)
    printf("\n--- Test 7: BackupTest -> Standby ---\n");
    input_tf7_3();  // Simulate backup test complete
    state_machine_process();
    printf("After TF7_3: Current state = %d (Expected: %d STANDBY)\n", 
           get_current_state(), STANDBY);
    
    printf("\n=== State Machine Test Complete ===\n");
}

// Test function to demonstrate alternative path
void test_alternative_path(void) {
    printf("\n=== Testing Alternative Path ===\n");
    
    // Reset to POWER_ON
    current_state = POWER_ON;
    printf("Reset to POWER_ON: %d\n", get_current_state());
    
    // PowerOn -> Manual (TF1_8)
    printf("\n--- Test: PowerOn -> Manual ---\n");
    input_tf1_8();  // Simulate manual mode request
    state_machine_process();
    printf("After TF1_8: Current state = %d (Expected: %d MANUAL)\n", 
           get_current_state(), MANUAL);
    
    // Manual -> Standby (TF8_3)
    printf("\n--- Test: Manual -> Standby ---\n");
    input_tf8_3();  // Simulate exit manual mode
    state_machine_process();
    printf("After TF8_3: Current state = %d (Expected: %d STANDBY)\n", 
           get_current_state(), STANDBY);
    
    // Test PreNormal -> Normal path
    printf("\n--- Test: Standby -> PreNormal -> Normal ---\n");
    input_tf3_6();  // Standby -> PreNormal
    state_machine_process();
    printf("After TF3_6: Current state = %d (PRE_NORMAL)\n", get_current_state());
    
    input_tf6_4();  // PreNormal -> Normal
    state_machine_process();
    printf("After TF6_4: Current state = %d (Expected: %d NORMAL)\n", 
           get_current_state(), NORMAL);
    
    // Normal -> Emergency (TF4_5)
    printf("\n--- Test: Normal -> Emergency ---\n");
    input_tf4_5();  // Simulate emergency in normal mode
    state_machine_process();
    printf("After TF4_5: Current state = %d (Expected: %d EMERGENCY)\n", 
           get_current_state(), EMERGENCY);
    
    printf("\n=== Alternative Path Test Complete ===\n");
}

// Display state counters
void display_state_counters(void) {
    printf("\n=== State Execution Counters ===\n");
    printf("PowerOn: %d\n", powerOn);
    printf("Standby: %d\n", standby);
    printf("PreNormal: %d\n", preNormal);
    printf("Normal: %d\n", normal);
    printf("Emergency: %d\n", emergency);
    printf("BackupTest: %d\n", backupTest);
    printf("Manual: %d\n", manual);
    printf("VortexRun: %d\n", vortexRun);
    printf("VortexBrake: %d\n", vortexBrake);
    printf("================================\n");
}

#ifdef TEST_MAIN
int main(void) {
    printf("State Machine Test Program\n");
    printf("Based on Updated Flowchart with Vortex Modes\n");
    printf("==========================================\n");
    
    // Run main test sequence
    test_state_machine_transitions();
    
    // Display counters after main test
    display_state_counters();
    
    // Run alternative path test
    test_alternative_path();
    
    // Display final counters
    display_state_counters();
    
    return 0;
}
#endif
