#include <stateMachine.h>
#include <externInputInterface.h>

#define GET_BIT(x, bit) ((x & (1 << bit)) >> bit)

int SET_BIT(int num, int bit_pos) {
    return num | (1 << bit_pos);  // 锟斤拷锟斤拷1锟斤拷位锟斤拷
}

int CLR_BIT(int num, int bit_pos) {
    return num & ~(1 << bit_pos);  // 锟斤拷锟斤拷1锟斤拷取锟斤拷锟斤拷锟劫帮拷位锟斤拷
}

// Transition condition variables
int TF1_8_condition = 0;
int TF1_3_condition = 0;
int TF3_6_condition = 0;
int TF6_4_condition = 0;
int TF6_5_condition = 0;
int TF9_10_condition = 0;
int TF10_5_condition = 0;
int TF4_5_condition = 0;
int TF5_7_condition = 0;
int TF7_3_condition = 0;
int TF8_3_condition = 0;
int TF5_3_condition = 0;
int TF9_5_condition = 0;

// State Action flag
int powerOn = 0;
int standby = 0;
int preNormal = 0;
int normal = 0;
int emergency = 0;
int backupTest = 0;
int manual = 0;
int vortexRun = 0;
int vortexBrake = 0;

SystemState current_state = POWER_ON;

/* 锟斤拷取锟斤拷前状态 */
SystemState get_current_state(void) {
    return current_state;
}

/* State handling functions */
static void handle_power_on(void);
static void handle_standby(void);
static void handle_pre_normal(void);
static void handle_normal(void);
static void handle_emergency(void);
static void handle_backup_test(void);
static void handle_manual(void);
static void handle_vortex_run(void);
static void handle_vortex_brake(void);

/* Transition check functions */
static int check_tf1_8(void);  // PowerOn->Manual
static int check_tf1_3(void);  // PowerOn->Standby
static int check_tf3_6(void);  // Standby->PreNormal
static int check_tf6_4(void);  // PreNormal->Normal
static int check_tf6_5(void);  // PreNormal->VortexRun
static int check_tf9_10(void); // VortexRun->VortexBrake
static int check_tf10_5(void); // VortexBrake->Emergency
static int check_tf4_5(void);  // Normal->Emergency
static int check_tf5_7(void);  // Emergency->BackupTest
static int check_tf7_3(void);  // BackupTest->Standby
static int check_tf8_3(void);  // Manual->Standby
static int check_tf5_3(void);  // Emergency->Standby
static int check_tf9_5(void);  // VortexRun->Emergency

/* 状态锟叫伙拷锟斤拷锟斤拷 */
static void transition_to(SystemState new_state) {
    current_state = new_state;
}

// Check transition conditions and execute state transitions according to flowchart
static void check_transitions(void) {
    switch (current_state) {
        case POWER_ON:
            if (check_tf1_8()) {             // PowerOn->Manual
                transition_to(MANUAL);
            } else if (check_tf1_3()) {      // PowerOn->Standby
                transition_to(STANDBY);
            }
            break;

        case STANDBY:
            if (check_tf3_6()) {             // Standby->PreNormal
                transition_to(PRE_NORMAL);
            }
            break;

        case PRE_NORMAL:
            if (check_tf6_4()) {             // PreNormal->Normal
                transition_to(NORMAL);
            } else if (check_tf6_5()) {      // PreNormal->VortexRun
                transition_to(VORTEX_RUN);
            }
            break;

        case NORMAL:
            if (check_tf4_5()) {             // Normal->Emergency
                transition_to(EMERGENCY);
            }
            break;

        case VORTEX_RUN:
            if (check_tf9_10()) {            // VortexRun->VortexBrake
                transition_to(VORTEX_BRAKE);
            } else if (check_tf9_5()) {      // VortexRun->Emergency
                transition_to(EMERGENCY);
            }
            break;

        case VORTEX_BRAKE:
            if (check_tf10_5()) {            // VortexBrake->Emergency
                transition_to(EMERGENCY);
            }
            break;

        case EMERGENCY:
            if (check_tf5_7()) {             // Emergency->BackupTest
                transition_to(BACKUP_TEST);
            } else if (check_tf5_3()) {      // Emergency->Standby
                transition_to(STANDBY);
            }
            break;

        case BACKUP_TEST:
            if (check_tf7_3()) {             // BackupTest->Standby
                transition_to(STANDBY);
            }
            break;

        case MANUAL:
            if (check_tf8_3()) {             // Manual->Standby
                transition_to(STANDBY);
            }
            break;
        default:
            // Invalid state, report error
            break;
    }
}

//锟斤拷锟阶刺拷锟阶拷锟斤拷锟斤拷锟斤拷锟斤拷锟角白刺拷锟斤拷呒锟斤拷锟斤拷锟�
/* 状态锟斤拷锟斤拷循锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷 */
void state_machine_process(void) {
    // 锟斤拷椴拷锟斤拷锟阶刺拷锟�
    check_transitions();
    
    // Execute current state processing logic
    switch (current_state) {
        case POWER_ON:
            handle_power_on();
            break;
        case STANDBY:
            handle_standby();
            break;
        case PRE_NORMAL:
            handle_pre_normal();
            break;
        case NORMAL:
            handle_normal();
            break;
        case EMERGENCY:
            handle_emergency();
            break;
        case BACKUP_TEST:
            handle_backup_test();
            break;
        case MANUAL:
            handle_manual();
            break;
        case VORTEX_RUN:
            handle_vortex_run();
            break;
        case VORTEX_BRAKE:
            handle_vortex_brake();
            break;
        default:
            // Invalid state, report error
            break;
    }

}

/* 状态锟斤拷锟斤拷锟斤拷锟斤拷实锟斤拷 */
static void handle_power_on(void) {
    powerOn = powerOn + 1;


    // 锟斤拷始锟斤拷系统硬锟斤拷
    // ...
}

static void handle_standby(void) {
    standby = standby + 1;
    // 锟酵癸拷锟斤拷模式
    // ...
}

static void handle_normal(void) {
    normal = normal + 1;
    // 锟斤拷锟斤拷业锟斤拷锟竭硷拷
    // ...
}

static void handle_emergency(void) {
    emergency = emergency + 1;
    // 锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟�
    // ...
}

static void handle_backup_test(void) {
    backupTest = backupTest + 1;
    // 锟襟备碉拷源锟斤拷锟斤拷锟竭硷拷
    // ...
}

static void handle_manual(void) {
    manual = manual + 1;
    // 锟街讹拷锟斤拷锟斤拷锟竭硷拷
    // ...
}

static void handle_pre_normal(void) {
    preNormal = preNormal + 1;
    // Pre-normal state logic
    // ...
}

static void handle_vortex_run(void) {
    vortexRun = vortexRun + 1;
    // Vortex run mode logic
    // ...
}

static void handle_vortex_brake(void) {
    vortexBrake = vortexBrake + 1;
    // Vortex brake mode logic
    // ...
}

/* Transition check function implementations */
static int check_tf1_8(void) {
    // PowerOn->Manual transition condition
    // Example: Check if manual mode is requested
    input_tf1_8();
    if (GET_BIT(TF1_8_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf1_3(void) {
    // PowerOn->Standby transition condition
    // Example: Check if power-on sequence is complete
    if (GET_BIT(TF1_3_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf3_6(void) {
    // Standby->PreNormal transition condition
    // Example: Check if normal operation is requested
    if (GET_BIT(TF3_6_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf6_4(void) {
    // PreNormal->Normal transition condition
    // Example: Check if pre-normal checks are complete
    if (GET_BIT(TF6_4_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf6_5(void) {
    // PreNormal->VortexRun transition condition
    // Example: Check if vortex mode is selected
    if (GET_BIT(TF6_5_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf9_10(void) {
    // VortexRun->VortexBrake transition condition
    // Example: Check if brake mode is triggered
    if (GET_BIT(TF9_10_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf10_5(void) {
    // VortexBrake->Emergency transition condition
    // Example: Check if emergency condition is detected
    if (GET_BIT(TF10_5_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf4_5(void) {
    // Normal->Emergency transition condition
    // Example: Check if emergency condition is detected
    if (GET_BIT(TF4_5_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf5_7(void) {
    // Emergency->BackupTest transition condition
    // Example: Check if backup test is requested
    if (GET_BIT(TF5_7_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf7_3(void) {
    // BackupTest->Standby transition condition
    // Example: Check if backup test is complete
    if (GET_BIT(TF7_3_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf8_3(void) {
    // Manual->Standby transition condition
    // Example: Check if manual mode exit is requested
    if (GET_BIT(TF8_3_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf5_3(void) {
    // Emergency->Standby transition condition
    // Example: Check if emergency is cleared
    if (GET_BIT(TF5_3_condition, 0)) {
        return 1;
    }
    return 0;
}

static int check_tf9_5(void) {
    // VortexRun->Emergency transition condition
    // Example: Check if emergency condition is detected in vortex mode
    if (GET_BIT(TF9_5_condition, 0)) {
        return 1;
    }
    return 0;
}
