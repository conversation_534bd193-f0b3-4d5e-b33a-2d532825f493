/**
 * @file stateMachine.c
 * @brief State machine implementation for pitch control system
 * @note Compliant with MISRA-C:2004 standard
 */

#include <stateMachine.h>
#include <externInputInterface.h>

/* Bit manipulation macros - MISRA-C compliant */
#define GET_BIT(x, bit) (((x) & (1U << (bit))) >> (bit))

/* Bit manipulation functions with range checking */
u16 setBit(u16 num, u8 bitPos) {
    u16 result = num;
    if (bitPos < 16U) {  /* Range check for u16 */
        result = num | (1U << bitPos);
    }
    return result;
}

u16 clrBit(u16 num, u8 bitPos) {
    u16 result = num;
    if (bitPos < 16U) {  /* Range check for u16 */
        result = num & (~(1U << bitPos));
    }
    return result;
}

/* Global transition condition variables - initialized to 0 */
u16 gTf18Condition = 0U;    /* PowerOn->Manual condition */
u16 gTf13Condition = 0U;    /* PowerOn->Standby condition */
u16 gTf36Condition = 0U;    /* Standby->PreNormal condition */
u16 gTf64Condition = 0U;    /* PreNormal->Normal condition */
u16 gTf65Condition = 0U;    /* PreNormal->VortexRun condition */
u16 gTf910Condition = 0U;   /* VortexRun->VortexBrake condition */
u16 gTf105Condition = 0U;   /* VortexBrake->Emergency condition */
u16 gTf45Condition = 0U;    /* Normal->Emergency condition */
u16 gTf57Condition = 0U;    /* Emergency->BackupTest condition */
u16 gTf73Condition = 0U;    /* BackupTest->Standby condition */
u16 gTf83Condition = 0U;    /* Manual->Standby condition */
u16 gTf53Condition = 0U;    /* Emergency->Standby condition */
u16 gTf95Condition = 0U;    /* VortexRun->Emergency condition */

/* Global state action counters - initialized to 0 */
u32 gPowerOnCnt = 0U;       /* Power on state counter */
u32 gStandbyCnt = 0U;       /* Standby state counter */
u32 gPreNormalCnt = 0U;     /* Pre-normal state counter */
u32 gNormalCnt = 0U;        /* Normal state counter */
u32 gEmergencyCnt = 0U;     /* Emergency state counter */
u32 gBackupTestCnt = 0U;    /* Backup test state counter */
u32 gManualCnt = 0U;        /* Manual state counter */
u32 gVortexRunCnt = 0U;     /* Vortex run state counter */
u32 gVortexBrakeCnt = 0U;   /* Vortex brake state counter */

/* Global current state with dual protection */
SystemState gCurrentState = POWER_ON;
SystemState gCurrentStateBak = POWER_ON;

/* Get current state with dual protection check */
SystemState getCurrentState(void) {
    SystemState state = gCurrentState;
    
    /* Critical function - dual protection check */
    if (gCurrentState != gCurrentStateBak) {
        /* State corruption detected - reset to safe state */
        gCurrentState = EMERGENCY;
        gCurrentStateBak = EMERGENCY;
        state = EMERGENCY;
    }
    
    return state;
}

/* Static function prototypes - internal use only */
static void handlePowerOn(void);
static void handleStandby(void);
static void handlePreNormal(void);
static void handleNormal(void);
static void handleEmergency(void);
static void handleBackupTest(void);
static void handleManual(void);
static void handleVortexRun(void);
static void handleVortexBrake(void);

/* Transition check functions */
static bool checkTf18(void);   /* PowerOn->Manual */
static bool checkTf13(void);   /* PowerOn->Standby */
static bool checkTf36(void);   /* Standby->PreNormal */
static bool checkTf64(void);   /* PreNormal->Normal */
static bool checkTf65(void);   /* PreNormal->VortexRun */
static bool checkTf910(void);  /* VortexRun->VortexBrake */
static bool checkTf105(void);  /* VortexBrake->Emergency */
static bool checkTf45(void);   /* Normal->Emergency */
static bool checkTf57(void);   /* Emergency->BackupTest */
static bool checkTf73(void);   /* BackupTest->Standby */
static bool checkTf83(void);   /* Manual->Standby */
static bool checkTf53(void);   /* Emergency->Standby */
static bool checkTf95(void);   /* VortexRun->Emergency */

/* State transition function with dual protection */
static void transitionTo(SystemState newState) {
    /* Range check for valid state */
    if (newState < STATE_MAX) {
        gCurrentState = newState;
        gCurrentStateBak = newState;  /* Dual protection */
    }
}

/* Check transition conditions - max nesting depth 2 levels */
static void checkTransitions(void) {
    SystemState currentState = getCurrentState();
    
    switch (currentState) {
        case POWER_ON:
            if (checkTf18() == TRUE) {
                transitionTo(MANUAL);
            } else if (checkTf13() == TRUE) {
                transitionTo(STANDBY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case STANDBY:
            if (checkTf36() == TRUE) {
                transitionTo(PRE_NORMAL);
            }
            break;

        case PRE_NORMAL:
            if (checkTf64() == TRUE) {
                transitionTo(NORMAL);
            } else if (checkTf65() == TRUE) {
                transitionTo(VORTEX_RUN);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case NORMAL:
            if (checkTf45() == TRUE) {
                transitionTo(EMERGENCY);
            }
            break;

        case VORTEX_RUN:
            if (checkTf910() == TRUE) {
                transitionTo(VORTEX_BRAKE);
            } else if (checkTf95() == TRUE) {
                transitionTo(EMERGENCY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case VORTEX_BRAKE:
            if (checkTf105() == TRUE) {
                transitionTo(EMERGENCY);
            }
            break;

        case EMERGENCY:
            if (checkTf57() == TRUE) {
                transitionTo(BACKUP_TEST);
            } else if (checkTf53() == TRUE) {
                transitionTo(STANDBY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case BACKUP_TEST:
            if (checkTf73() == TRUE) {
                transitionTo(STANDBY);
            }
            break;

        case MANUAL:
            if (checkTf83() == TRUE) {
                transitionTo(STANDBY);
            }
            break;
            
        default:
            /* Invalid state - transition to emergency */
            transitionTo(EMERGENCY);
            break;
    }
}

/* Main state machine processing function - max 100 lines */
void stateMachineProcess(void) {
    SystemState currentState;
    
    /* Check and execute state transitions */
    checkTransitions();
    
    /* Get current state for processing */
    currentState = getCurrentState();
    
    /* Execute current state processing logic */
    switch (currentState) {
        case POWER_ON:
            handlePowerOn();
            break;
        case STANDBY:
            handleStandby();
            break;
        case PRE_NORMAL:
            handlePreNormal();
            break;
        case NORMAL:
            handleNormal();
            break;
        case EMERGENCY:
            handleEmergency();
            break;
        case BACKUP_TEST:
            handleBackupTest();
            break;
        case MANUAL:
            handleManual();
            break;
        case VORTEX_RUN:
            handleVortexRun();
            break;
        case VORTEX_BRAKE:
            handleVortexBrake();
            break;
        default:
            /* Invalid state - force to emergency */
            transitionTo(EMERGENCY);
            break;
    }
}

/* State handling function implementations */
static void handlePowerOn(void) {
    /* Increment counter with overflow protection */
    if (gPowerOnCnt < 0xFFFFFFFFU) {
        gPowerOnCnt++;
    }
    
    /* Initialize system hardware */
    /* Add specific power-on logic here */
}

static void handleStandby(void) {
    /* Increment counter with overflow protection */
    if (gStandbyCnt < 0xFFFFFFFFU) {
        gStandbyCnt++;
    }
    
    /* Standby mode logic */
    /* Add specific standby logic here */
}

static void handlePreNormal(void) {
    /* Increment counter with overflow protection */
    if (gPreNormalCnt < 0xFFFFFFFFU) {
        gPreNormalCnt++;
    }
    
    /* Pre-normal state logic */
    /* Add specific pre-normal logic here */
}

static void handleNormal(void) {
    /* Increment counter with overflow protection */
    if (gNormalCnt < 0xFFFFFFFFU) {
        gNormalCnt++;
    }
    
    /* Normal operation logic */
    /* Add specific normal operation logic here */
}

static void handleEmergency(void) {
    /* Increment counter with overflow protection */
    if (gEmergencyCnt < 0xFFFFFFFFU) {
        gEmergencyCnt++;
    }

    /* Emergency handling logic */
    /* Add specific emergency logic here */
}

static void handleBackupTest(void) {
    /* Increment counter with overflow protection */
    if (gBackupTestCnt < 0xFFFFFFFFU) {
        gBackupTestCnt++;
    }

    /* Backup test logic */
    /* Add specific backup test logic here */
}

static void handleManual(void) {
    /* Increment counter with overflow protection */
    if (gManualCnt < 0xFFFFFFFFU) {
        gManualCnt++;
    }

    /* Manual mode logic */
    /* Add specific manual mode logic here */
}

static void handleVortexRun(void) {
    /* Increment counter with overflow protection */
    if (gVortexRunCnt < 0xFFFFFFFFU) {
        gVortexRunCnt++;
    }

    /* Vortex run mode logic */
    /* Add specific vortex run logic here */
}

static void handleVortexBrake(void) {
    /* Increment counter with overflow protection */
    if (gVortexBrakeCnt < 0xFFFFFFFFU) {
        gVortexBrakeCnt++;
    }

    /* Vortex brake mode logic */
    /* Add specific vortex brake logic here */
}

/* Transition check function implementations */
static bool checkTf18(void) {
    /* PowerOn->Manual transition condition */
    return (GET_BIT(gTf18Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf13(void) {
    /* PowerOn->Standby transition condition */
    return (GET_BIT(gTf13Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf36(void) {
    /* Standby->PreNormal transition condition */
    return (GET_BIT(gTf36Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf64(void) {
    /* PreNormal->Normal transition condition */
    return (GET_BIT(gTf64Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf65(void) {
    /* PreNormal->VortexRun transition condition */
    return (GET_BIT(gTf65Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf910(void) {
    /* VortexRun->VortexBrake transition condition */
    return (GET_BIT(gTf910Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf105(void) {
    /* VortexBrake->Emergency transition condition */
    return (GET_BIT(gTf105Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf45(void) {
    /* Normal->Emergency transition condition */
    return (GET_BIT(gTf45Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf57(void) {
    /* Emergency->BackupTest transition condition */
    return (GET_BIT(gTf57Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf73(void) {
    /* BackupTest->Standby transition condition */
    return (GET_BIT(gTf73Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf83(void) {
    /* Manual->Standby transition condition */
    return (GET_BIT(gTf83Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf53(void) {
    /* Emergency->Standby transition condition */
    return (GET_BIT(gTf53Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool checkTf95(void) {
    /* VortexRun->Emergency transition condition */
    return (GET_BIT(gTf95Condition, 0U) == 1U) ? TRUE : FALSE;
}
