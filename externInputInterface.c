#include <externInputInterface.h>
#include <stateMachine.h>
int DI = 0;

// External input assignment to transition conditions
void input_tf1_8(void) {
    // PowerOn->Manual: Example - manual mode button pressed
    DI = SET_BIT(DI, 0);
    TF1_8_condition = DI;
}

void input_tf1_3(void) {
    // PowerOn->Standby: Example - power-on sequence complete
    DI = SET_BIT(DI, 0);
    TF1_3_condition = DI;
}

void input_tf3_6(void) {
    // Standby->PreNormal: Example - start operation requested
    DI = SET_BIT(DI, 0);
    TF3_6_condition = DI;
}

void input_tf6_4(void) {
    // PreNormal->Normal: Example - pre-checks complete
    DI = SET_BIT(DI, 0);
    TF6_4_condition = DI;
}

void input_tf6_5(void) {
    // PreNormal->VortexRun: Example - vortex mode selected
    DI = SET_BIT(DI, 0);
    TF6_5_condition = DI;
}

void input_tf9_10(void) {
    // VortexRun->VortexBrake: Example - brake mode triggered
    DI = SET_BIT(DI, 0);
    TF9_10_condition = DI;
}

void input_tf10_5(void) {
    // VortexBrake->Emergency: Example - emergency detected
    DI = SET_BIT(DI, 0);
    TF10_5_condition = DI;
}

void input_tf4_5(void) {
    // Normal->Emergency: Example - emergency detected
    DI = SET_BIT(DI, 0);
    TF4_5_condition = DI;
}

void input_tf5_7(void) {
    // Emergency->BackupTest: Example - backup test requested
    DI = SET_BIT(DI, 0);
    TF5_7_condition = DI;
}

void input_tf7_3(void) {
    // BackupTest->Standby: Example - backup test complete
    DI = SET_BIT(DI, 0);
    TF7_3_condition = DI;
}

void input_tf8_3(void) {
    // Manual->Standby: Example - exit manual mode
    DI = SET_BIT(DI, 0);
    TF8_3_condition = DI;
}

void input_tf5_3(void) {
    // Emergency->Standby: Example - emergency cleared
    DI = SET_BIT(DI, 0);
    TF5_3_condition = DI;
}

void input_tf9_5(void) {
    // VortexRun->Emergency: Example - emergency in vortex mode
    DI = SET_BIT(DI, 0);
    TF9_5_condition = DI;
}
