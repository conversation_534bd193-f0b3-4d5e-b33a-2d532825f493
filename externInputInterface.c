/**
 * @file externInputInterface.c
 * @brief External input interface implementation
 * @note Compliant with MISRA-C:2004 standard
 */

#include <externInputInterface.h>
#include <stateMachine.h>

/* Global digital input register - with 'g' prefix */
static u16 gDigitalInput = 0U;

/* External input assignment to transition conditions */
void inputTf18(void) {
    /* PowerOn->Manual: Example - manual mode button pressed */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf18Condition = gDigitalInput;
}

void inputTf13(void) {
    /* PowerOn->Standby: Example - power-on sequence complete */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf13Condition = gDigitalInput;
}

void inputTf36(void) {
    /* Standby->PreNormal: Example - start operation requested */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf36Condition = gDigitalInput;
}

void inputTf64(void) {
    /* PreNormal->Normal: Example - pre-checks complete */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf64Condition = gDigitalInput;
}

void inputTf65(void) {
    /* PreNormal->VortexRun: Example - vortex mode selected */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf65Condition = gDigitalInput;
}

void inputTf910(void) {
    /* VortexRun->VortexBrake: Example - brake mode triggered */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf910Condition = gDigitalInput;
}

void inputTf105(void) {
    /* VortexBrake->Emergency: Example - emergency detected */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf105Condition = gDigitalInput;
}

void inputTf45(void) {
    /* Normal->Emergency: Example - emergency detected */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf45Condition = gDigitalInput;
}

void inputTf57(void) {
    /* Emergency->BackupTest: Example - backup test requested */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf57Condition = gDigitalInput;
}

void inputTf73(void) {
    /* BackupTest->Standby: Example - backup test complete */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf73Condition = gDigitalInput;
}

void inputTf83(void) {
    /* Manual->Standby: Example - exit manual mode */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf83Condition = gDigitalInput;
}

void inputTf53(void) {
    /* Emergency->Standby: Example - emergency cleared */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf53Condition = gDigitalInput;
}

void inputTf95(void) {
    /* VortexRun->Emergency: Example - emergency in vortex mode */
    gDigitalInput = setBit(gDigitalInput, 0U);
    gTf95Condition = gDigitalInput;
}
