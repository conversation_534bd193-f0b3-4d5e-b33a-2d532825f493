<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TMS320C2000 Linker PC v18.1.4.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x686b973f</link_time>
   <link_errors>0x0</link_errors>
   <output_file>testStateMachineAugment.out</output_file>
   <entry_point>
      <name>_c_int00</name>
      <address>0x55b8</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-2">
         <path>.\</path>
         <kind>object</kind>
         <file>CodeStartBranch.obj</file>
         <name>CodeStartBranch.obj</name>
      </input_file>
      <input_file id="fl-3">
         <path>.\</path>
         <kind>object</kind>
         <file>externInputInterface.obj</file>
         <name>externInputInterface.obj</name>
      </input_file>
      <input_file id="fl-4">
         <path>.\</path>
         <kind>object</kind>
         <file>main.obj</file>
         <name>main.obj</name>
      </input_file>
      <input_file id="fl-5">
         <path>.\</path>
         <kind>object</kind>
         <file>stateMachine.obj</file>
         <name>stateMachine.obj</name>
      </input_file>
      <input_file id="fl-6">
         <path>.\</path>
         <kind>object</kind>
         <file>test_state_machine.obj</file>
         <name>test_state_machine.obj</name>
      </input_file>
      <input_file id="fl-a">
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>boot28.asm.obj</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memcpy.c.obj</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-1a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>startup.c.obj</name>
      </input_file>
      <input_file id="fl-1b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-1c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-1d">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-1e">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>printf.c.obj</name>
      </input_file>
      <input_file id="fl-1f">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-20">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-21">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>ctype.c.obj</name>
      </input_file>
      <input_file id="fl-22">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-23">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fputc.c.obj</name>
      </input_file>
      <input_file id="fl-24">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-25">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-26">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memchr.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memset.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>strchr.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>strcpy.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>s_copysign.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_add28.asm.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_cmp28.asm.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_mpy28.asm.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_neg28.asm.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_sub28.asm.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_toi28.asm.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fd_tol28.asm.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>ll_aox28.asm.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>ll_cmp28.asm.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>ll_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>i_tofd28.asm.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>l_tofd28.asm.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>l_div28.asm.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fs_tofdfpu32.asm.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>errno.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>strcmp.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>strncpy.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>D:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\</path>
         <kind>archive</kind>
         <file>rts2800_fpu32.lib</file>
         <name>close.c.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-27">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0x5c</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-72">
         <name>.cinit</name>
         <load_address>0x405c</load_address>
         <run_address>0x405c</run_address>
         <size>0x2a</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.cinit</name>
         <load_address>0x4086</load_address>
         <run_address>0x4086</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.cinit</name>
         <load_address>0x40af</load_address>
         <run_address>0x40af</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.cinit</name>
         <load_address>0x40bd</load_address>
         <run_address>0x40bd</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.cinit:__lock</name>
         <load_address>0x40c7</load_address>
         <run_address>0x40c7</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.cinit:__unlock</name>
         <load_address>0x40cc</load_address>
         <run_address>0x40cc</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-18">
         <name>.cinit</name>
         <load_address>0x40d1</load_address>
         <run_address>0x40d1</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.cinit</name>
         <load_address>0x40d5</load_address>
         <run_address>0x40d5</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.cinit</name>
         <load_address>0x40d9</load_address>
         <run_address>0x40d9</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-153">
         <name>.cinit</name>
         <load_address>0x40dd</load_address>
         <run_address>0x40dd</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-162">
         <name>.cinit</name>
         <load_address>0x40e1</load_address>
         <run_address>0x40e1</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0x167</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text</name>
         <load_address>0x5167</load_address>
         <run_address>0x5167</run_address>
         <size>0x107</size>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.text</name>
         <load_address>0x526e</load_address>
         <run_address>0x526e</run_address>
         <size>0xa9</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text</name>
         <load_address>0x5317</load_address>
         <run_address>0x5317</run_address>
         <size>0x9c</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text</name>
         <load_address>0x53b3</load_address>
         <run_address>0x53b3</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.text</name>
         <load_address>0x53b4</load_address>
         <run_address>0x53b4</run_address>
         <size>0x95</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.text</name>
         <load_address>0x5449</load_address>
         <run_address>0x5449</run_address>
         <size>0x8b</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text</name>
         <load_address>0x54d4</load_address>
         <run_address>0x54d4</run_address>
         <size>0x83</size>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text</name>
         <load_address>0x5557</load_address>
         <run_address>0x5557</run_address>
         <size>0x61</size>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-11">
         <name>.text</name>
         <load_address>0x55b8</load_address>
         <run_address>0x55b8</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-190">
         <name>.text</name>
         <load_address>0x560e</load_address>
         <run_address>0x560e</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-192">
         <name>.text</name>
         <load_address>0x564e</load_address>
         <run_address>0x564e</run_address>
         <size>0x3f</size>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-196">
         <name>.text</name>
         <load_address>0x568d</load_address>
         <run_address>0x568d</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-193">
         <name>.text</name>
         <load_address>0x56cb</load_address>
         <run_address>0x56cb</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text</name>
         <load_address>0x5703</load_address>
         <run_address>0x5703</run_address>
         <size>0x2a</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text</name>
         <load_address>0x572d</load_address>
         <run_address>0x572d</run_address>
         <size>0x2a</size>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.text</name>
         <load_address>0x5757</load_address>
         <run_address>0x5757</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text</name>
         <load_address>0x5780</load_address>
         <run_address>0x5780</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.text</name>
         <load_address>0x57a9</load_address>
         <run_address>0x57a9</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.text</name>
         <load_address>0x57d1</load_address>
         <run_address>0x57d1</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-195">
         <name>.text</name>
         <load_address>0x57f5</load_address>
         <run_address>0x57f5</run_address>
         <size>0x24</size>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text</name>
         <load_address>0x5819</load_address>
         <run_address>0x5819</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text</name>
         <load_address>0x583a</load_address>
         <run_address>0x583a</run_address>
         <size>0x1f</size>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text</name>
         <load_address>0x5859</load_address>
         <run_address>0x5859</run_address>
         <size>0x1e</size>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text</name>
         <load_address>0x5877</load_address>
         <run_address>0x5877</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-197">
         <name>.text</name>
         <load_address>0x5894</load_address>
         <run_address>0x5894</run_address>
         <size>0x1d</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text</name>
         <load_address>0x58b1</load_address>
         <run_address>0x58b1</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text</name>
         <load_address>0x58cd</load_address>
         <run_address>0x58cd</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text</name>
         <load_address>0x58e9</load_address>
         <run_address>0x58e9</run_address>
         <size>0x19</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text</name>
         <load_address>0x5902</load_address>
         <run_address>0x5902</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text</name>
         <load_address>0x591a</load_address>
         <run_address>0x591a</run_address>
         <size>0xe</size>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-21">
         <name>.text:retain</name>
         <load_address>0x5928</load_address>
         <run_address>0x5928</run_address>
         <size>0xa</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.text</name>
         <load_address>0x5932</load_address>
         <run_address>0x5932</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text</name>
         <load_address>0x593b</load_address>
         <run_address>0x593b</run_address>
         <size>0x9</size>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.text</name>
         <load_address>0x5944</load_address>
         <run_address>0x5944</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.text</name>
         <load_address>0x594c</load_address>
         <run_address>0x594c</run_address>
         <size>0x5</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text</name>
         <load_address>0x5951</load_address>
         <run_address>0x5951</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-12">
         <name>codestart</name>
         <load_address>0x4106</load_address>
         <run_address>0x4106</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-198">
         <name>ramfuncs</name>
         <load_address>0x40e7</load_address>
         <run_address>0x40e7</run_address>
         <size>0x1f</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-34">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-33">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <run_address>0x0</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-19">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x87ff</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-20">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8890</run_address>
         <size>0x4</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-28">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x87e0</run_address>
         <size>0x17</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x867a</run_address>
         <size>0x6</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.ebss:__unlock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8896</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-199">
         <name>.ebss:__lock</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8894</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-73">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8680</run_address>
         <size>0x160</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8800</run_address>
         <size>0x88</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8898</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-133">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x87f7</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-154">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8888</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x8899</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-163">
         <name>.ebss</name>
         <uninitialized>true</uninitialized>
         <run_address>0x889a</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-155">
         <name>.esysmem</name>
         <uninitialized>true</uninitialized>
         <run_address>0x9000</run_address>
         <size>0x1</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.esysmem</name>
         <uninitialized>true</uninitialized>
         <run_address>0x9000</run_address>
         <size>0x0</size>
      </object_component>
      <object_component id="oc-2e">
         <name>.econst:.string</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x612</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-65">
         <name>.econst:.string</name>
         <load_address>0x8612</load_address>
         <run_address>0x8612</run_address>
         <size>0x26</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-64">
         <name>.econst</name>
         <load_address>0x8638</load_address>
         <run_address>0x8638</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.econst</name>
         <load_address>0x8654</load_address>
         <run_address>0x8654</run_address>
         <size>0x1c</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.econst</name>
         <load_address>0x8670</load_address>
         <run_address>0x8670</run_address>
         <size>0x8</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.econst:.string</name>
         <load_address>0x8678</load_address>
         <run_address>0x8678</run_address>
         <size>0x2</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-13">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd3</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17">
         <name>.debug_info</name>
         <load_address>0xd3</load_address>
         <run_address>0xd3</run_address>
         <size>0xbaf</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0xc82</load_address>
         <run_address>0xc82</run_address>
         <size>0x5fd</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-26">
         <name>.debug_info</name>
         <load_address>0x127f</load_address>
         <run_address>0x127f</run_address>
         <size>0x15c1</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2d">
         <name>.debug_info</name>
         <load_address>0x2840</load_address>
         <run_address>0x2840</run_address>
         <size>0x6d5</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x2f15</load_address>
         <run_address>0x2f15</run_address>
         <size>0x176</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_info</name>
         <load_address>0x308b</load_address>
         <run_address>0x308b</run_address>
         <size>0x58a</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_info</name>
         <load_address>0x3615</load_address>
         <run_address>0x3615</run_address>
         <size>0x485</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_info</name>
         <load_address>0x3a9a</load_address>
         <run_address>0x3a9a</run_address>
         <size>0x3f8</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x3e92</load_address>
         <run_address>0x3e92</run_address>
         <size>0x3f5</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x4287</load_address>
         <run_address>0x4287</run_address>
         <size>0x58f</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_info</name>
         <load_address>0x4816</load_address>
         <run_address>0x4816</run_address>
         <size>0x517</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_info</name>
         <load_address>0x4d2d</load_address>
         <run_address>0x4d2d</run_address>
         <size>0x4b2</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_info</name>
         <load_address>0x51df</load_address>
         <run_address>0x51df</run_address>
         <size>0x5ad</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_info</name>
         <load_address>0x578c</load_address>
         <run_address>0x578c</run_address>
         <size>0x89f</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_info</name>
         <load_address>0x602b</load_address>
         <run_address>0x602b</run_address>
         <size>0x3d6</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_info</name>
         <load_address>0x6401</load_address>
         <run_address>0x6401</run_address>
         <size>0x1c8</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_info</name>
         <load_address>0x65c9</load_address>
         <run_address>0x65c9</run_address>
         <size>0x2e2</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_info</name>
         <load_address>0x68ab</load_address>
         <run_address>0x68ab</run_address>
         <size>0x535</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0x6de0</load_address>
         <run_address>0x6de0</run_address>
         <size>0x5b7</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_info</name>
         <load_address>0x7397</load_address>
         <run_address>0x7397</run_address>
         <size>0x3bd</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0x7754</load_address>
         <run_address>0x7754</run_address>
         <size>0x3b5</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0x7b09</load_address>
         <run_address>0x7b09</run_address>
         <size>0x3c7</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_info</name>
         <load_address>0x7ed0</load_address>
         <run_address>0x7ed0</run_address>
         <size>0x3c1</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_info</name>
         <load_address>0x8291</load_address>
         <run_address>0x8291</run_address>
         <size>0x3be</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_info</name>
         <load_address>0x864f</load_address>
         <run_address>0x864f</run_address>
         <size>0x535</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0x8b84</load_address>
         <run_address>0x8b84</run_address>
         <size>0x4fb</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0x907f</load_address>
         <run_address>0x907f</run_address>
         <size>0x741</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_info</name>
         <load_address>0x97c0</load_address>
         <run_address>0x97c0</run_address>
         <size>0x407</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_info</name>
         <load_address>0x9bc7</load_address>
         <run_address>0x9bc7</run_address>
         <size>0x471</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_info</name>
         <load_address>0xa038</load_address>
         <run_address>0xa038</run_address>
         <size>0x3d9</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_info</name>
         <load_address>0xa411</load_address>
         <run_address>0xa411</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_info</name>
         <load_address>0xa528</load_address>
         <run_address>0xa528</run_address>
         <size>0x123</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_info</name>
         <load_address>0xa64b</load_address>
         <run_address>0xa64b</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_info</name>
         <load_address>0xa762</load_address>
         <run_address>0xa762</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_info</name>
         <load_address>0xa879</load_address>
         <run_address>0xa879</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_info</name>
         <load_address>0xa990</load_address>
         <run_address>0xa990</run_address>
         <size>0x125</size>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0xaab5</load_address>
         <run_address>0xaab5</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_info</name>
         <load_address>0xabcc</load_address>
         <run_address>0xabcc</run_address>
         <size>0x117</size>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_info</name>
         <load_address>0xace3</load_address>
         <run_address>0xace3</run_address>
         <size>0x179</size>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_info</name>
         <load_address>0xae5c</load_address>
         <run_address>0xae5c</run_address>
         <size>0x151</size>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_info</name>
         <load_address>0xafad</load_address>
         <run_address>0xafad</run_address>
         <size>0x234</size>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_info</name>
         <load_address>0xb1e1</load_address>
         <run_address>0xb1e1</run_address>
         <size>0x11b</size>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_info</name>
         <load_address>0xb2fc</load_address>
         <run_address>0xb2fc</run_address>
         <size>0x11b</size>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_info</name>
         <load_address>0xb417</load_address>
         <run_address>0xb417</run_address>
         <size>0x1a8</size>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_info</name>
         <load_address>0xb5bf</load_address>
         <run_address>0xb5bf</run_address>
         <size>0x11d</size>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_info</name>
         <load_address>0xb6dc</load_address>
         <run_address>0xb6dc</run_address>
         <size>0x3c9</size>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_info</name>
         <load_address>0xbaa5</load_address>
         <run_address>0xbaa5</run_address>
         <size>0x1c6</size>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_info</name>
         <load_address>0xbc6b</load_address>
         <run_address>0xbc6b</run_address>
         <size>0x4c6</size>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_info</name>
         <load_address>0xc131</load_address>
         <run_address>0xc131</run_address>
         <size>0x4f6</size>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_info</name>
         <load_address>0xc627</load_address>
         <run_address>0xc627</run_address>
         <size>0x4e1</size>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_info</name>
         <load_address>0xcb08</load_address>
         <run_address>0xcb08</run_address>
         <size>0x5da</size>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_info</name>
         <load_address>0xd0e2</load_address>
         <run_address>0xd0e2</run_address>
         <size>0x533</size>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-128">
         <name>.debug_info</name>
         <load_address>0xd615</load_address>
         <run_address>0xd615</run_address>
         <size>0x579</size>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_info</name>
         <load_address>0xdb8e</load_address>
         <run_address>0xdb8e</run_address>
         <size>0x5d1</size>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_info</name>
         <load_address>0xe15f</load_address>
         <run_address>0xe15f</run_address>
         <size>0x5e2</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_info</name>
         <load_address>0xe741</load_address>
         <run_address>0xe741</run_address>
         <size>0x5ac</size>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_info</name>
         <load_address>0xeced</load_address>
         <run_address>0xeced</run_address>
         <size>0x65b</size>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_info</name>
         <load_address>0xf348</load_address>
         <run_address>0xf348</run_address>
         <size>0x59b</size>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_info</name>
         <load_address>0xf8e3</load_address>
         <run_address>0xf8e3</run_address>
         <size>0x5cb</size>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_info</name>
         <load_address>0xfeae</load_address>
         <run_address>0xfeae</run_address>
         <size>0x5db</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-152">
         <name>.debug_info</name>
         <load_address>0x10489</load_address>
         <run_address>0x10489</run_address>
         <size>0x56c</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_info</name>
         <load_address>0x109f5</load_address>
         <run_address>0x109f5</run_address>
         <size>0x5d8</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_info</name>
         <load_address>0x10fcd</load_address>
         <run_address>0x10fcd</run_address>
         <size>0x635</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-168">
         <name>.debug_info</name>
         <load_address>0x11602</load_address>
         <run_address>0x11602</run_address>
         <size>0x550</size>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_info</name>
         <load_address>0x11b52</load_address>
         <run_address>0x11b52</run_address>
         <size>0x644</size>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_info</name>
         <load_address>0x12196</load_address>
         <run_address>0x12196</run_address>
         <size>0x3c4</size>
         <input_file_ref idref="fl-53"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_info</name>
         <load_address>0x1255a</load_address>
         <run_address>0x1255a</run_address>
         <size>0x3a4</size>
         <input_file_ref idref="fl-54"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_info</name>
         <load_address>0x128fe</load_address>
         <run_address>0x128fe</run_address>
         <size>0x3db</size>
         <input_file_ref idref="fl-55"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_info</name>
         <load_address>0x12cd9</load_address>
         <run_address>0x12cd9</run_address>
         <size>0x5ca</size>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_info</name>
         <load_address>0x132a3</load_address>
         <run_address>0x132a3</run_address>
         <size>0x604</size>
         <input_file_ref idref="fl-57"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.debug_info</name>
         <load_address>0x138a7</load_address>
         <run_address>0x138a7</run_address>
         <size>0xa0</size>
      </object_component>
      <object_component id="oc-14">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_line</name>
         <load_address>0x45</load_address>
         <run_address>0x45</run_address>
         <size>0x17a</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x1bf</load_address>
         <run_address>0x1bf</run_address>
         <size>0xef</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2a">
         <name>.debug_line</name>
         <load_address>0x2ae</load_address>
         <run_address>0x2ae</run_address>
         <size>0x3b4</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x662</load_address>
         <run_address>0x662</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_line</name>
         <load_address>0x69a</load_address>
         <run_address>0x69a</run_address>
         <size>0x7e</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_line</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0x50</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_line</name>
         <load_address>0x768</load_address>
         <run_address>0x768</run_address>
         <size>0x62</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_line</name>
         <load_address>0x7ca</load_address>
         <run_address>0x7ca</run_address>
         <size>0x3e</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0x808</load_address>
         <run_address>0x808</run_address>
         <size>0x3a</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x842</load_address>
         <run_address>0x842</run_address>
         <size>0x65</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_line</name>
         <load_address>0x8a7</load_address>
         <run_address>0x8a7</run_address>
         <size>0x5e</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_line</name>
         <load_address>0x905</load_address>
         <run_address>0x905</run_address>
         <size>0x55</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_line</name>
         <load_address>0x95a</load_address>
         <run_address>0x95a</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_line</name>
         <load_address>0x983</load_address>
         <run_address>0x983</run_address>
         <size>0x113</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0xa96</load_address>
         <run_address>0xa96</run_address>
         <size>0x27</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_line</name>
         <load_address>0xabd</load_address>
         <run_address>0xabd</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_line</name>
         <load_address>0xae5</load_address>
         <run_address>0xae5</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0xb0d</load_address>
         <run_address>0xb0d</run_address>
         <size>0x27</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0xb34</load_address>
         <run_address>0xb34</run_address>
         <size>0x2a</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_line</name>
         <load_address>0xb5e</load_address>
         <run_address>0xb5e</run_address>
         <size>0x92</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_line</name>
         <load_address>0xbf0</load_address>
         <run_address>0xbf0</run_address>
         <size>0x92</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0xc82</load_address>
         <run_address>0xc82</run_address>
         <size>0x92</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_line</name>
         <load_address>0xd14</load_address>
         <run_address>0xd14</run_address>
         <size>0xab</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_line</name>
         <load_address>0xdbf</load_address>
         <run_address>0xdbf</run_address>
         <size>0xb1</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_line</name>
         <load_address>0xe70</load_address>
         <run_address>0xe70</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0xe98</load_address>
         <run_address>0xe98</run_address>
         <size>0x2a</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_line</name>
         <load_address>0xec2</load_address>
         <run_address>0xec2</run_address>
         <size>0x2b</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0xeed</load_address>
         <run_address>0xeed</run_address>
         <size>0x2d</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_line</name>
         <load_address>0xf1a</load_address>
         <run_address>0xf1a</run_address>
         <size>0xc4</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_line</name>
         <load_address>0xfde</load_address>
         <run_address>0xfde</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_line</name>
         <load_address>0x103e</load_address>
         <run_address>0x103e</run_address>
         <size>0xb3</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_line</name>
         <load_address>0x10f1</load_address>
         <run_address>0x10f1</run_address>
         <size>0xa2</size>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_line</name>
         <load_address>0x1193</load_address>
         <run_address>0x1193</run_address>
         <size>0x45</size>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_line</name>
         <load_address>0x11d8</load_address>
         <run_address>0x11d8</run_address>
         <size>0x49</size>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_line</name>
         <load_address>0x1221</load_address>
         <run_address>0x1221</run_address>
         <size>0x59</size>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_line</name>
         <load_address>0x127a</load_address>
         <run_address>0x127a</run_address>
         <size>0x58</size>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_line</name>
         <load_address>0x12d2</load_address>
         <run_address>0x12d2</run_address>
         <size>0x6c</size>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_line</name>
         <load_address>0x133e</load_address>
         <run_address>0x133e</run_address>
         <size>0x63</size>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_line</name>
         <load_address>0x13a1</load_address>
         <run_address>0x13a1</run_address>
         <size>0x16b</size>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_line</name>
         <load_address>0x150c</load_address>
         <run_address>0x150c</run_address>
         <size>0x57</size>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_line</name>
         <load_address>0x1563</load_address>
         <run_address>0x1563</run_address>
         <size>0x56</size>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_line</name>
         <load_address>0x15b9</load_address>
         <run_address>0x15b9</run_address>
         <size>0x8e</size>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_line</name>
         <load_address>0x1647</load_address>
         <run_address>0x1647</run_address>
         <size>0x58</size>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_line</name>
         <load_address>0x169f</load_address>
         <run_address>0x169f</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_line</name>
         <load_address>0x16c8</load_address>
         <run_address>0x16c8</run_address>
         <size>0x2b</size>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_line</name>
         <load_address>0x16f3</load_address>
         <run_address>0x16f3</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_line</name>
         <load_address>0x171c</load_address>
         <run_address>0x171c</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_line</name>
         <load_address>0x1744</load_address>
         <run_address>0x1744</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_line</name>
         <load_address>0x176c</load_address>
         <run_address>0x176c</run_address>
         <size>0x2a</size>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_line</name>
         <load_address>0x1796</load_address>
         <run_address>0x1796</run_address>
         <size>0x54</size>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_line</name>
         <load_address>0x17ea</load_address>
         <run_address>0x17ea</run_address>
         <size>0x72</size>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_line</name>
         <load_address>0x185c</load_address>
         <run_address>0x185c</run_address>
         <size>0x52</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_line</name>
         <load_address>0x18ae</load_address>
         <run_address>0x18ae</run_address>
         <size>0x61</size>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_line</name>
         <load_address>0x190f</load_address>
         <run_address>0x190f</run_address>
         <size>0x5c</size>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_line</name>
         <load_address>0x196b</load_address>
         <run_address>0x196b</run_address>
         <size>0x4f</size>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-149">
         <name>.debug_line</name>
         <load_address>0x19ba</load_address>
         <run_address>0x19ba</run_address>
         <size>0x61</size>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_line</name>
         <load_address>0x1a1b</load_address>
         <run_address>0x1a1b</run_address>
         <size>0xaa</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_line</name>
         <load_address>0x1ac5</load_address>
         <run_address>0x1ac5</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_line</name>
         <load_address>0x1aee</load_address>
         <run_address>0x1aee</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_line</name>
         <load_address>0x1b16</load_address>
         <run_address>0x1b16</run_address>
         <size>0x27</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.debug_line</name>
         <load_address>0x1b3d</load_address>
         <run_address>0x1b3d</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_line</name>
         <load_address>0x1b66</load_address>
         <run_address>0x1b66</run_address>
         <size>0x2c</size>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_line</name>
         <load_address>0x1b92</load_address>
         <run_address>0x1b92</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-53"/>
      </object_component>
      <object_component id="oc-179">
         <name>.debug_line</name>
         <load_address>0x1bbb</load_address>
         <run_address>0x1bbb</run_address>
         <size>0x92</size>
         <input_file_ref idref="fl-54"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_line</name>
         <load_address>0x1c4d</load_address>
         <run_address>0x1c4d</run_address>
         <size>0x92</size>
         <input_file_ref idref="fl-55"/>
      </object_component>
      <object_component id="oc-183">
         <name>.debug_line</name>
         <load_address>0x1cdf</load_address>
         <run_address>0x1cdf</run_address>
         <size>0x29</size>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-188">
         <name>.debug_line</name>
         <load_address>0x1d08</load_address>
         <run_address>0x1d08</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-57"/>
      </object_component>
      <object_component id="oc-15">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x21</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.debug_abbrev</name>
         <load_address>0x21</load_address>
         <run_address>0x21</run_address>
         <size>0xaa</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-24">
         <name>.debug_abbrev</name>
         <load_address>0xcb</load_address>
         <run_address>0xcb</run_address>
         <size>0xf7</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2b">
         <name>.debug_abbrev</name>
         <load_address>0x1c2</load_address>
         <run_address>0x1c2</run_address>
         <size>0x18b</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_abbrev</name>
         <load_address>0x34d</load_address>
         <run_address>0x34d</run_address>
         <size>0xfa</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_abbrev</name>
         <load_address>0x447</load_address>
         <run_address>0x447</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x48d</load_address>
         <run_address>0x48d</run_address>
         <size>0xe3</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0x570</load_address>
         <run_address>0x570</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_abbrev</name>
         <load_address>0x618</load_address>
         <run_address>0x618</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_abbrev</name>
         <load_address>0x680</load_address>
         <run_address>0x680</run_address>
         <size>0x66</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_abbrev</name>
         <load_address>0x6e6</load_address>
         <run_address>0x6e6</run_address>
         <size>0x130</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_abbrev</name>
         <load_address>0x816</load_address>
         <run_address>0x816</run_address>
         <size>0xb2</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_abbrev</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0xee</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_abbrev</name>
         <load_address>0x9b6</load_address>
         <run_address>0x9b6</run_address>
         <size>0x165</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_abbrev</name>
         <load_address>0xb1b</load_address>
         <run_address>0xb1b</run_address>
         <size>0x1a3</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_abbrev</name>
         <load_address>0xcbe</load_address>
         <run_address>0xcbe</run_address>
         <size>0xc1</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_abbrev</name>
         <load_address>0xd7f</load_address>
         <run_address>0xd7f</run_address>
         <size>0x57</size>
         <input_file_ref idref="fl-21"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_abbrev</name>
         <load_address>0xdd6</load_address>
         <run_address>0xdd6</run_address>
         <size>0x76</size>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_abbrev</name>
         <load_address>0xe4c</load_address>
         <run_address>0xe4c</run_address>
         <size>0x124</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0xf70</load_address>
         <run_address>0xf70</run_address>
         <size>0x12b</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_abbrev</name>
         <load_address>0x109b</load_address>
         <run_address>0x109b</run_address>
         <size>0xca</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x1165</load_address>
         <run_address>0x1165</run_address>
         <size>0x9d</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_abbrev</name>
         <load_address>0x1202</load_address>
         <run_address>0x1202</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_abbrev</name>
         <load_address>0x12aa</load_address>
         <run_address>0x12aa</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_abbrev</name>
         <load_address>0x1352</load_address>
         <run_address>0x1352</run_address>
         <size>0x9d</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_abbrev</name>
         <load_address>0x13ef</load_address>
         <run_address>0x13ef</run_address>
         <size>0xa4</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_abbrev</name>
         <load_address>0x1493</load_address>
         <run_address>0x1493</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_abbrev</name>
         <load_address>0x153b</load_address>
         <run_address>0x153b</run_address>
         <size>0x123</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_abbrev</name>
         <load_address>0x165e</load_address>
         <run_address>0x165e</run_address>
         <size>0xb6</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_abbrev</name>
         <load_address>0x1714</load_address>
         <run_address>0x1714</run_address>
         <size>0xdf</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_abbrev</name>
         <load_address>0x17f3</load_address>
         <run_address>0x17f3</run_address>
         <size>0xb6</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_abbrev</name>
         <load_address>0x18a9</load_address>
         <run_address>0x18a9</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_abbrev</name>
         <load_address>0x18e1</load_address>
         <run_address>0x18e1</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_abbrev</name>
         <load_address>0x1919</load_address>
         <run_address>0x1919</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x1951</load_address>
         <run_address>0x1951</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_abbrev</name>
         <load_address>0x1989</load_address>
         <run_address>0x1989</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_abbrev</name>
         <load_address>0x19c1</load_address>
         <run_address>0x19c1</run_address>
         <size>0x46</size>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_abbrev</name>
         <load_address>0x1a07</load_address>
         <run_address>0x1a07</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0x1a3f</load_address>
         <run_address>0x1a3f</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0x1a77</load_address>
         <run_address>0x1a77</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_abbrev</name>
         <load_address>0x1aaf</load_address>
         <run_address>0x1aaf</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_abbrev</name>
         <load_address>0x1ae7</load_address>
         <run_address>0x1ae7</run_address>
         <size>0x5f</size>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_abbrev</name>
         <load_address>0x1b46</load_address>
         <run_address>0x1b46</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_abbrev</name>
         <load_address>0x1b7e</load_address>
         <run_address>0x1b7e</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_abbrev</name>
         <load_address>0x1bb6</load_address>
         <run_address>0x1bb6</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_abbrev</name>
         <load_address>0x1bee</load_address>
         <run_address>0x1bee</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_abbrev</name>
         <load_address>0x1c26</load_address>
         <run_address>0x1c26</run_address>
         <size>0xa8</size>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_abbrev</name>
         <load_address>0x1cce</load_address>
         <run_address>0x1cce</run_address>
         <size>0x31</size>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_abbrev</name>
         <load_address>0x1cff</load_address>
         <run_address>0x1cff</run_address>
         <size>0xf1</size>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_abbrev</name>
         <load_address>0x1df0</load_address>
         <run_address>0x1df0</run_address>
         <size>0x124</size>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_abbrev</name>
         <load_address>0x1f14</load_address>
         <run_address>0x1f14</run_address>
         <size>0x110</size>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_abbrev</name>
         <load_address>0x2024</load_address>
         <run_address>0x2024</run_address>
         <size>0x111</size>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_abbrev</name>
         <load_address>0x2135</load_address>
         <run_address>0x2135</run_address>
         <size>0x135</size>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_abbrev</name>
         <load_address>0x226a</load_address>
         <run_address>0x226a</run_address>
         <size>0x107</size>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0x2371</load_address>
         <run_address>0x2371</run_address>
         <size>0x107</size>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_abbrev</name>
         <load_address>0x2478</load_address>
         <run_address>0x2478</run_address>
         <size>0x129</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_abbrev</name>
         <load_address>0x25a1</load_address>
         <run_address>0x25a1</run_address>
         <size>0x107</size>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-140">
         <name>.debug_abbrev</name>
         <load_address>0x26a8</load_address>
         <run_address>0x26a8</run_address>
         <size>0x120</size>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_abbrev</name>
         <load_address>0x27c8</load_address>
         <run_address>0x27c8</run_address>
         <size>0x119</size>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_abbrev</name>
         <load_address>0x28e1</load_address>
         <run_address>0x28e1</run_address>
         <size>0x107</size>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_abbrev</name>
         <load_address>0x29e8</load_address>
         <run_address>0x29e8</run_address>
         <size>0xc6</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-158">
         <name>.debug_abbrev</name>
         <load_address>0x2aae</load_address>
         <run_address>0x2aae</run_address>
         <size>0x1b4</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_abbrev</name>
         <load_address>0x2c62</load_address>
         <run_address>0x2c62</run_address>
         <size>0x18f</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_abbrev</name>
         <load_address>0x2df1</load_address>
         <run_address>0x2df1</run_address>
         <size>0x138</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.debug_abbrev</name>
         <load_address>0x2f29</load_address>
         <run_address>0x2f29</run_address>
         <size>0x134</size>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_abbrev</name>
         <load_address>0x305d</load_address>
         <run_address>0x305d</run_address>
         <size>0x11e</size>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_abbrev</name>
         <load_address>0x317b</load_address>
         <run_address>0x317b</run_address>
         <size>0xb6</size>
         <input_file_ref idref="fl-53"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_abbrev</name>
         <load_address>0x3231</load_address>
         <run_address>0x3231</run_address>
         <size>0x9d</size>
         <input_file_ref idref="fl-54"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_abbrev</name>
         <load_address>0x32ce</load_address>
         <run_address>0x32ce</run_address>
         <size>0xaf</size>
         <input_file_ref idref="fl-55"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_abbrev</name>
         <load_address>0x337d</load_address>
         <run_address>0x337d</run_address>
         <size>0x12d</size>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-189">
         <name>.debug_abbrev</name>
         <load_address>0x34aa</load_address>
         <run_address>0x34aa</run_address>
         <size>0x118</size>
         <input_file_ref idref="fl-57"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.debug_abbrev</name>
         <load_address>0x35c2</load_address>
         <run_address>0x35c2</run_address>
         <size>0xf</size>
      </object_component>
      <object_component id="oc-16">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x80</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-25">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-2c">
         <name>.debug_aranges</name>
         <load_address>0xe8</load_address>
         <run_address>0xe8</run_address>
         <size>0xf8</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_aranges</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_aranges</name>
         <load_address>0x1f8</load_address>
         <run_address>0x1f8</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_aranges</name>
         <load_address>0x218</load_address>
         <run_address>0x218</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_aranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_aranges</name>
         <load_address>0x258</load_address>
         <run_address>0x258</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_aranges</name>
         <load_address>0x278</load_address>
         <run_address>0x278</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_aranges</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_aranges</name>
         <load_address>0x2c0</load_address>
         <run_address>0x2c0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_aranges</name>
         <load_address>0x2f0</load_address>
         <run_address>0x2f0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_aranges</name>
         <load_address>0x310</load_address>
         <run_address>0x310</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_aranges</name>
         <load_address>0x328</load_address>
         <run_address>0x328</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_aranges</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_aranges</name>
         <load_address>0x358</load_address>
         <run_address>0x358</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_aranges</name>
         <load_address>0x370</load_address>
         <run_address>0x370</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_aranges</name>
         <load_address>0x388</load_address>
         <run_address>0x388</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_aranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_aranges</name>
         <load_address>0x3b8</load_address>
         <run_address>0x3b8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_aranges</name>
         <load_address>0x3d0</load_address>
         <run_address>0x3d0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_aranges</name>
         <load_address>0x3e8</load_address>
         <run_address>0x3e8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_aranges</name>
         <load_address>0x400</load_address>
         <run_address>0x400</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_aranges</name>
         <load_address>0x420</load_address>
         <run_address>0x420</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_aranges</name>
         <load_address>0x458</load_address>
         <run_address>0x458</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_aranges</name>
         <load_address>0x470</load_address>
         <run_address>0x470</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_aranges</name>
         <load_address>0x488</load_address>
         <run_address>0x488</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_aranges</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_aranges</name>
         <load_address>0x4c0</load_address>
         <run_address>0x4c0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_aranges</name>
         <load_address>0x4e0</load_address>
         <run_address>0x4e0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_aranges</name>
         <load_address>0x500</load_address>
         <run_address>0x500</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_aranges</name>
         <load_address>0x520</load_address>
         <run_address>0x520</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_aranges</name>
         <load_address>0x540</load_address>
         <run_address>0x540</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_aranges</name>
         <load_address>0x560</load_address>
         <run_address>0x560</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_aranges</name>
         <load_address>0x580</load_address>
         <run_address>0x580</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_aranges</name>
         <load_address>0x5a0</load_address>
         <run_address>0x5a0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-38"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_aranges</name>
         <load_address>0x5d0</load_address>
         <run_address>0x5d0</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-39"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_aranges</name>
         <load_address>0x5f8</load_address>
         <run_address>0x5f8</run_address>
         <size>0x40</size>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_aranges</name>
         <load_address>0x638</load_address>
         <run_address>0x638</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_aranges</name>
         <load_address>0x658</load_address>
         <run_address>0x658</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_aranges</name>
         <load_address>0x678</load_address>
         <run_address>0x678</run_address>
         <size>0x38</size>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_aranges</name>
         <load_address>0x6b0</load_address>
         <run_address>0x6b0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-3e"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_aranges</name>
         <load_address>0x6d0</load_address>
         <run_address>0x6d0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_aranges</name>
         <load_address>0x6e8</load_address>
         <run_address>0x6e8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_aranges</name>
         <load_address>0x700</load_address>
         <run_address>0x700</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_aranges</name>
         <load_address>0x718</load_address>
         <run_address>0x718</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-122">
         <name>.debug_aranges</name>
         <load_address>0x730</load_address>
         <run_address>0x730</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_aranges</name>
         <load_address>0x748</load_address>
         <run_address>0x748</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_aranges</name>
         <load_address>0x760</load_address>
         <run_address>0x760</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_aranges</name>
         <load_address>0x780</load_address>
         <run_address>0x780</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_aranges</name>
         <load_address>0x7a0</load_address>
         <run_address>0x7a0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_aranges</name>
         <load_address>0x7c0</load_address>
         <run_address>0x7c0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-141">
         <name>.debug_aranges</name>
         <load_address>0x7e0</load_address>
         <run_address>0x7e0</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_aranges</name>
         <load_address>0x800</load_address>
         <run_address>0x800</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_aranges</name>
         <load_address>0x820</load_address>
         <run_address>0x820</run_address>
         <size>0x20</size>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-151">
         <name>.debug_aranges</name>
         <load_address>0x840</load_address>
         <run_address>0x840</run_address>
         <size>0x28</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_aranges</name>
         <load_address>0x868</load_address>
         <run_address>0x868</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_aranges</name>
         <load_address>0x880</load_address>
         <run_address>0x880</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-167">
         <name>.debug_aranges</name>
         <load_address>0x898</load_address>
         <run_address>0x898</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.debug_aranges</name>
         <load_address>0x8b0</load_address>
         <run_address>0x8b0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_aranges</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_aranges</name>
         <load_address>0x8e0</load_address>
         <run_address>0x8e0</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-53"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_aranges</name>
         <load_address>0x8f8</load_address>
         <run_address>0x8f8</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-54"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_aranges</name>
         <load_address>0x910</load_address>
         <run_address>0x910</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-55"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_aranges</name>
         <load_address>0x928</load_address>
         <run_address>0x928</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_aranges</name>
         <load_address>0x940</load_address>
         <run_address>0x940</run_address>
         <size>0x18</size>
         <input_file_ref idref="fl-57"/>
      </object_component>
      <object_component id="oc-1a">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x168</size>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x168</load_address>
         <run_address>0x168</run_address>
         <size>0xd4</size>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-29">
         <name>.debug_frame</name>
         <load_address>0x23c</load_address>
         <run_address>0x23c</run_address>
         <size>0x2dc</size>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.debug_frame</name>
         <load_address>0x518</load_address>
         <run_address>0x518</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_frame</name>
         <load_address>0x548</load_address>
         <run_address>0x548</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_frame</name>
         <load_address>0x5b0</load_address>
         <run_address>0x5b0</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_frame</name>
         <load_address>0x5f8</load_address>
         <run_address>0x5f8</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_frame</name>
         <load_address>0x640</load_address>
         <run_address>0x640</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1a"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_frame</name>
         <load_address>0x688</load_address>
         <run_address>0x688</run_address>
         <size>0x68</size>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_frame</name>
         <load_address>0x6f0</load_address>
         <run_address>0x6f0</run_address>
         <size>0x78</size>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_frame</name>
         <load_address>0x768</load_address>
         <run_address>0x768</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_frame</name>
         <load_address>0x7e0</load_address>
         <run_address>0x7e0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0x810</load_address>
         <run_address>0x810</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_frame</name>
         <load_address>0x840</load_address>
         <run_address>0x840</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_frame</name>
         <load_address>0x870</load_address>
         <run_address>0x870</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_frame</name>
         <load_address>0x8a0</load_address>
         <run_address>0x8a0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_frame</name>
         <load_address>0x8d0</load_address>
         <run_address>0x8d0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_frame</name>
         <load_address>0x900</load_address>
         <run_address>0x900</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_frame</name>
         <load_address>0x930</load_address>
         <run_address>0x930</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_frame</name>
         <load_address>0x960</load_address>
         <run_address>0x960</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_frame</name>
         <load_address>0x990</load_address>
         <run_address>0x990</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_frame</name>
         <load_address>0x9d8</load_address>
         <run_address>0x9d8</run_address>
         <size>0x48</size>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_frame</name>
         <load_address>0xa20</load_address>
         <run_address>0xa20</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-2c"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0xa50</load_address>
         <run_address>0xa50</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_frame</name>
         <load_address>0xa80</load_address>
         <run_address>0xa80</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_frame</name>
         <load_address>0xab0</load_address>
         <run_address>0xab0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_frame</name>
         <load_address>0xae0</load_address>
         <run_address>0xae0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_frame</name>
         <load_address>0xb10</load_address>
         <run_address>0xb10</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_frame</name>
         <load_address>0xb40</load_address>
         <run_address>0xb40</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_frame</name>
         <load_address>0xb70</load_address>
         <run_address>0xb70</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_frame</name>
         <load_address>0xba0</load_address>
         <run_address>0xba0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_frame</name>
         <load_address>0xbd0</load_address>
         <run_address>0xbd0</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_frame</name>
         <load_address>0xc00</load_address>
         <run_address>0xc00</run_address>
         <size>0x58</size>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_frame</name>
         <load_address>0xc58</load_address>
         <run_address>0xc58</run_address>
         <size>0x6c</size>
         <input_file_ref idref="fl-47"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_frame</name>
         <load_address>0xcc4</load_address>
         <run_address>0xcc4</run_address>
         <size>0x6c</size>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_frame</name>
         <load_address>0xd30</load_address>
         <run_address>0xd30</run_address>
         <size>0x6c</size>
         <input_file_ref idref="fl-49"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_frame</name>
         <load_address>0xd9c</load_address>
         <run_address>0xd9c</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-4a"/>
      </object_component>
      <object_component id="oc-143">
         <name>.debug_frame</name>
         <load_address>0xdfc</load_address>
         <run_address>0xdfc</run_address>
         <size>0x58</size>
         <input_file_ref idref="fl-4b"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_frame</name>
         <load_address>0xe54</load_address>
         <run_address>0xe54</run_address>
         <size>0x60</size>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_frame</name>
         <load_address>0xeb4</load_address>
         <run_address>0xeb4</run_address>
         <size>0x80</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_frame</name>
         <load_address>0xf34</load_address>
         <run_address>0xf34</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-4e"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_frame</name>
         <load_address>0xf64</load_address>
         <run_address>0xf64</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-4f"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_frame</name>
         <load_address>0xf94</load_address>
         <run_address>0xf94</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-50"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_frame</name>
         <load_address>0xfc4</load_address>
         <run_address>0xfc4</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-51"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_frame</name>
         <load_address>0xff4</load_address>
         <run_address>0xff4</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-52"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_frame</name>
         <load_address>0x1024</load_address>
         <run_address>0x1024</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-53"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_frame</name>
         <load_address>0x1054</load_address>
         <run_address>0x1054</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-54"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_frame</name>
         <load_address>0x1084</load_address>
         <run_address>0x1084</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-55"/>
      </object_component>
      <object_component id="oc-182">
         <name>.debug_frame</name>
         <load_address>0x10b4</load_address>
         <run_address>0x10b4</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-56"/>
      </object_component>
      <object_component id="oc-187">
         <name>.debug_frame</name>
         <load_address>0x10e4</load_address>
         <run_address>0x10e4</run_address>
         <size>0x30</size>
         <input_file_ref idref="fl-57"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.cio</name>
         <uninitialized>true</uninitialized>
         <run_address>0x400</run_address>
         <size>0x120</size>
         <input_file_ref idref="fl-4d"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x4000</load_address>
         <run_address>0x4000</run_address>
         <size>0xe7</size>
         <contents>
            <object_component_ref idref="oc-27"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-18"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-162"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x5000</load_address>
         <run_address>0x5000</run_address>
         <size>0x953</size>
         <contents>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-11"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-18b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>codestart</name>
         <load_address>0x4106</load_address>
         <run_address>0x4106</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-12"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>ramfuncs</name>
         <load_address>0x40e7</load_address>
         <run_address>0x40e7</run_address>
         <size>0x1f</size>
         <contents>
            <object_component_ref idref="oc-198"/>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.reset</name>
         <load_address>0x3fffc0</load_address>
         <run_address>0x3fffc0</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-34"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>vectors</name>
         <run_address>0x3fffc2</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x0</run_address>
         <size>0x300</size>
         <contents>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-1c0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.ebss</name>
         <run_address>0x867a</run_address>
         <size>0x221</size>
         <contents>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-28"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-163"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.esysmem</name>
         <run_address>0x9000</run_address>
         <size>0x400</size>
         <contents>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-1c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.econst</name>
         <load_address>0x8000</load_address>
         <run_address>0x8000</run_address>
         <size>0x67a</size>
         <contents>
            <object_component_ref idref="oc-2e"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-7b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.switch</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>IQmath</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>IQmathTables</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1b2" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13947</size>
         <contents>
            <object_component_ref idref="oc-13"/>
            <object_component_ref idref="oc-17"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-26"/>
            <object_component_ref idref="oc-2d"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-1c2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b4" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d30</size>
         <contents>
            <object_component_ref idref="oc-14"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-2a"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-188"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b6" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x35d1</size>
         <contents>
            <object_component_ref idref="oc-15"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-24"/>
            <object_component_ref idref="oc-2b"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-1c3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b8" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x958</size>
         <contents>
            <object_component_ref idref="oc-16"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-25"/>
            <object_component_ref idref="oc-2c"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-18a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ba" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1114</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-29"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-187"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1bc" display="no" color="cyan">
         <name>.cio</name>
         <run_address>0x400</run_address>
         <size>0x120</size>
         <contents>
            <object_component_ref idref="oc-14d"/>
         </contents>
      </logical_group>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>ZONE0</name>
         <page_id>0x0</page_id>
         <origin>0x4000</origin>
         <length>0x1000</length>
         <used_space>0x108</used_space>
         <unused_space>0xef8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x4000</start_address>
               <size>0xe7</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x40e7</start_address>
               <size>0x1f</size>
               <logical_group_ref idref="lg-6"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x4106</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x4108</start_address>
               <size>0xef8</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE1</name>
         <page_id>0x0</page_id>
         <origin>0x5000</origin>
         <length>0x1000</length>
         <used_space>0x953</used_space>
         <unused_space>0x6ad</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x5000</start_address>
               <size>0x953</size>
               <logical_group_ref idref="lg-4"/>
            </allocated_space>
            <available_space>
               <start_address>0x5953</start_address>
               <size>0x6ad</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE2</name>
         <page_id>0x0</page_id>
         <origin>0x6000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE3</name>
         <page_id>0x0</page_id>
         <origin>0x7000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE4</name>
         <page_id>0x0</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE5</name>
         <page_id>0x0</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE6</name>
         <page_id>0x0</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>ZONE7</name>
         <page_id>0x0</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>CSM_RSVD</name>
         <page_id>0x0</page_id>
         <origin>0x33ff80</origin>
         <length>0x76</length>
         <used_space>0x0</used_space>
         <unused_space>0x76</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>CSM_PWL</name>
         <page_id>0x0</page_id>
         <origin>0x33fff8</origin>
         <length>0x8</length>
         <used_space>0x0</used_space>
         <unused_space>0x8</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>ADC_CAL</name>
         <page_id>0x0</page_id>
         <origin>0x380080</origin>
         <length>0x9</length>
         <used_space>0x0</used_space>
         <unused_space>0x9</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>RESET</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc0</origin>
         <length>0x2</length>
         <used_space>0x0</used_space>
         <unused_space>0x2</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>VECTORS</name>
         <page_id>0x0</page_id>
         <origin>0x3fffc2</origin>
         <length>0x3e</length>
         <used_space>0x0</used_space>
         <unused_space>0x3e</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x0</origin>
         <length>0x400</length>
         <used_space>0x300</used_space>
         <unused_space>0x100</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x300</size>
               <logical_group_ref idref="lg-9"/>
            </allocated_space>
            <available_space>
               <start_address>0x300</start_address>
               <size>0x100</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>M1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x400</origin>
         <length>0x400</length>
         <used_space>0x120</used_space>
         <unused_space>0x2e0</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x400</start_address>
               <size>0x120</size>
               <logical_group_ref idref="lg-1bc"/>
            </allocated_space>
            <available_space>
               <start_address>0x520</start_address>
               <size>0x2e0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="no" color="green">
         <name>PIEDATA</name>
         <page_id>0x1</page_id>
         <origin>0xce0</origin>
         <length>0x20</length>
         <used_space>0x0</used_space>
         <unused_space>0x20</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L0SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x8000</origin>
         <length>0x1000</length>
         <used_space>0x89b</used_space>
         <unused_space>0x765</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x8000</start_address>
               <size>0x67a</size>
               <logical_group_ref idref="lg-c"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x867a</start_address>
               <size>0x221</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <available_space>
               <start_address>0x889b</start_address>
               <size>0x765</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L1SARAM</name>
         <page_id>0x1</page_id>
         <origin>0x9000</origin>
         <length>0x1000</length>
         <used_space>0x400</used_space>
         <unused_space>0xc00</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x9000</start_address>
               <size>0x400</size>
               <logical_group_ref idref="lg-b"/>
            </allocated_space>
            <available_space>
               <start_address>0x9400</start_address>
               <size>0xc00</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L2SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xa000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>L3SARAM</name>
         <page_id>0x1</page_id>
         <origin>0xb000</origin>
         <length>0x1000</length>
         <used_space>0x0</used_space>
         <unused_space>0x1000</unused_space>
         <attributes>RWIX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <symbol_table>
      <symbol id="sm-0">
         <name>cinit</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-1">
         <name>___cinit__</name>
         <value>0x4000</value>
      </symbol>
      <symbol id="sm-2">
         <name>pinit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3">
         <name>___pinit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-5">
         <name>___binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6">
         <name>__STACK_SIZE</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-7">
         <name>__STACK_END</name>
         <value>0x300</value>
      </symbol>
      <symbol id="sm-8">
         <name>__SYSMEM_SIZE</name>
         <value>0x400</value>
      </symbol>
      <symbol id="sm-9">
         <name>___c_args__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-a">
         <name>.text</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-b">
         <name>___text__</name>
         <value>0x5000</value>
      </symbol>
      <symbol id="sm-c">
         <name>etext</name>
         <value>0x5953</value>
      </symbol>
      <symbol id="sm-d">
         <name>___etext__</name>
         <value>0x5953</value>
      </symbol>
      <symbol id="sm-e">
         <name>___TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>___TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>___TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-44">
         <name>code_start</name>
         <value>0x4106</value>
         <object_component_ref idref="oc-12"/>
      </symbol>
      <symbol id="sm-54">
         <name>_input_tf6_5</name>
         <value>0x52a2</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-55">
         <name>_input_tf6_4</name>
         <value>0x5295</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-56">
         <name>_input_tf9_5</name>
         <value>0x530a</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-57">
         <name>_input_tf5_7</name>
         <value>0x52d6</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-58">
         <name>_input_tf5_3</name>
         <value>0x52fd</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-59">
         <name>_input_tf1_3</name>
         <value>0x527b</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-5a">
         <name>_input_tf1_8</name>
         <value>0x526e</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-5b">
         <name>_DI</name>
         <value>0x87ff</value>
         <object_component_ref idref="oc-19"/>
      </symbol>
      <symbol id="sm-5c">
         <name>_input_tf8_3</name>
         <value>0x52f0</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-5d">
         <name>_input_tf4_5</name>
         <value>0x52c9</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-5e">
         <name>_input_tf10_5</name>
         <value>0x52bc</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-5f">
         <name>_input_tf7_3</name>
         <value>0x52e3</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-60">
         <name>_input_tf3_6</name>
         <value>0x5288</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-61">
         <name>_input_tf9_10</name>
         <value>0x52af</value>
         <object_component_ref idref="oc-1a3"/>
      </symbol>
      <symbol id="sm-7e">
         <name>_main</name>
         <value>0x40fc</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-7f">
         <name>_InitSystem</name>
         <value>0x40e7</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-80">
         <name>_DefaultIsr</name>
         <value>0x5928</value>
         <object_component_ref idref="oc-21"/>
      </symbol>
      <symbol id="sm-81">
         <name>__c_int00</name>
         <value>0x57cb</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-82">
         <name>_DelayMs</name>
         <value>0x57a9</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-83">
         <name>_InitPieVectTable</name>
         <value>0x57c7</value>
         <object_component_ref idref="oc-1a0"/>
      </symbol>
      <symbol id="sm-e1">
         <name>_TF6_5_condition</name>
         <value>0x87e5</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e2">
         <name>_TF6_4_condition</name>
         <value>0x87f6</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e3">
         <name>_TF8_3_condition</name>
         <value>0x87e1</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e4">
         <name>_normal</name>
         <value>0x87f0</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e5">
         <name>_TF3_6_condition</name>
         <value>0x87f4</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e6">
         <name>_SET_BIT</name>
         <value>0x5000</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-e7">
         <name>_TF4_5_condition</name>
         <value>0x87e7</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e8">
         <name>_manual</name>
         <value>0x87e9</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-e9">
         <name>_TF1_3_condition</name>
         <value>0x87f5</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-ea">
         <name>_CLR_BIT</name>
         <value>0x5009</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-eb">
         <name>_TF9_10_condition</name>
         <value>0x87e6</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-ec">
         <name>_TF1_8_condition</name>
         <value>0x87f3</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-ed">
         <name>_TF7_3_condition</name>
         <value>0x87e0</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-ee">
         <name>_vortexBrake</name>
         <value>0x87eb</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-ef">
         <name>_powerOn</name>
         <value>0x87e3</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f0">
         <name>_vortexRun</name>
         <value>0x87ec</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f1">
         <name>_TF9_5_condition</name>
         <value>0x87e4</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f2">
         <name>_TF5_3_condition</name>
         <value>0x87e2</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f3">
         <name>_TF10_5_condition</name>
         <value>0x87e8</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f4">
         <name>_TF5_7_condition</name>
         <value>0x87ef</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f5">
         <name>_preNormal</name>
         <value>0x87f1</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f6">
         <name>_standby</name>
         <value>0x87ee</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f7">
         <name>_emergency</name>
         <value>0x87f2</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-f8">
         <name>_get_current_state</name>
         <value>0x5013</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-f9">
         <name>_current_state</name>
         <value>0x87ed</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-fa">
         <name>_backupTest</name>
         <value>0x87ea</value>
         <object_component_ref idref="oc-28"/>
      </symbol>
      <symbol id="sm-fb">
         <name>_state_machine_process</name>
         <value>0x50a1</value>
         <object_component_ref idref="oc-1a1"/>
      </symbol>
      <symbol id="sm-149">
         <name>_c_int00</name>
         <value>0x55b8</value>
         <object_component_ref idref="oc-11"/>
      </symbol>
      <symbol id="sm-14a">
         <name>__stack</name>
         <value>0x0</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-159">
         <name>_copy_in</name>
         <value>0x57d1</value>
         <object_component_ref idref="oc-18c"/>
      </symbol>
      <symbol id="sm-16a">
         <name>_memcpy</name>
         <value>0x5894</value>
         <object_component_ref idref="oc-197"/>
      </symbol>
      <symbol id="sm-176">
         <name>__system_pre_init</name>
         <value>0x5951</value>
         <object_component_ref idref="oc-18b"/>
      </symbol>
      <symbol id="sm-183">
         <name>__system_post_cinit</name>
         <value>0x53b3</value>
         <object_component_ref idref="oc-18d"/>
      </symbol>
      <symbol id="sm-197">
         <name>C$$EXIT</name>
         <value>0x5757</value>
         <object_component_ref idref="oc-18f"/>
      </symbol>
      <symbol id="sm-198">
         <name>_exit</name>
         <value>0x5759</value>
         <object_component_ref idref="oc-18f"/>
      </symbol>
      <symbol id="sm-199">
         <name>___TI_cleanup_ptr</name>
         <value>0x867c</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-19a">
         <name>___TI_enable_exit_profile_output</name>
         <value>0x867a</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-19b">
         <name>_abort</name>
         <value>0x5757</value>
         <object_component_ref idref="oc-18f"/>
      </symbol>
      <symbol id="sm-19c">
         <name>___TI_dtors_ptr</name>
         <value>0x867e</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-1b0">
         <name>__unlock</name>
         <value>0x8896</value>
         <object_component_ref idref="oc-19d"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>__lock</name>
         <value>0x8894</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-1b2">
         <name>__register_lock</name>
         <value>0x5936</value>
         <object_component_ref idref="oc-1a2"/>
      </symbol>
      <symbol id="sm-1b3">
         <name>__nop</name>
         <value>0x593a</value>
         <object_component_ref idref="oc-1a2"/>
      </symbol>
      <symbol id="sm-1b4">
         <name>__register_unlock</name>
         <value>0x5932</value>
         <object_component_ref idref="oc-1a2"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>__args_main</name>
         <value>0x58e9</value>
         <object_component_ref idref="oc-18e"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>___TI_ft_end</name>
         <value>0x8680</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>___TI_tmpnams</name>
         <value>0x8740</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>__ftable</name>
         <value>0x86c0</value>
         <object_component_ref idref="oc-73"/>
      </symbol>
      <symbol id="sm-24d">
         <name>_strcpy</name>
         <value>0x594c</value>
         <object_component_ref idref="oc-19f"/>
      </symbol>
      <symbol id="sm-25a">
         <name>_strlen</name>
         <value>0x5944</value>
         <object_component_ref idref="oc-19b"/>
      </symbol>
      <symbol id="sm-26a">
         <name>__device</name>
         <value>0x8840</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-26b">
         <name>__stream</name>
         <value>0x8800</value>
         <object_component_ref idref="oc-a5"/>
      </symbol>
      <symbol id="sm-2ae">
         <name>FD$$ADD</name>
         <value>0x5317</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-2be">
         <name>FD$$CMP</name>
         <value>0x5703</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>FD$$DIV</name>
         <value>0x5449</value>
         <object_component_ref idref="oc-c5"/>
      </symbol>
      <symbol id="sm-2e7">
         <name>FD$$MPY</name>
         <value>0x54d4</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-2f4">
         <name>FD$$NEG</name>
         <value>0x593b</value>
         <object_component_ref idref="oc-cf"/>
      </symbol>
      <symbol id="sm-300">
         <name>FD$$SUB</name>
         <value>0x591a</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-30f">
         <name>FD$$TOI</name>
         <value>0x5819</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-31e">
         <name>FD$$TOL</name>
         <value>0x583a</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-32a">
         <name>LL$$AND</name>
         <value>0x5902</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-32b">
         <name>LL$$OR</name>
         <value>0x590a</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-32c">
         <name>LL$$XOR</name>
         <value>0x5912</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-33a">
         <name>ULL$$CMP</name>
         <value>0x586b</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-33b">
         <name>LL$$CMP</name>
         <value>0x5859</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-367">
         <name>ULL$$MOD</name>
         <value>0x520c</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-368">
         <name>ULL$$DIV</name>
         <value>0x51dd</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-369">
         <name>LL$$MOD</name>
         <value>0x51a3</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-36a">
         <name>LL$$DIV</name>
         <value>0x5167</value>
         <object_component_ref idref="oc-ed"/>
      </symbol>
      <symbol id="sm-37a">
         <name>I$$TOFD</name>
         <value>0x5877</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-38a">
         <name>L$$TOFD</name>
         <value>0x58cd</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-396">
         <name>UL$$MOD</name>
         <value>0x5751</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-397">
         <name>L$$MOD</name>
         <value>0x573c</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-398">
         <name>L$$DIV</name>
         <value>0x572d</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-399">
         <name>UL$$DIV</name>
         <value>0x574a</value>
         <object_component_ref idref="oc-fc"/>
      </symbol>
      <symbol id="sm-3a9">
         <name>FS$$TOFD</name>
         <value>0x58b1</value>
         <object_component_ref idref="oc-101"/>
      </symbol>
      <symbol id="sm-3bc">
         <name>_errno</name>
         <value>0x8898</value>
         <object_component_ref idref="oc-10d"/>
      </symbol>
      <symbol id="sm-3f5">
         <name>_HOSTclose</name>
         <value>0x5780</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-401">
         <name>_HOSTlseek</name>
         <value>0x5557</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-410">
         <name>_parmbuf</name>
         <value>0x87f7</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-411">
         <name>_HOSTopen</name>
         <value>0x560e</value>
         <object_component_ref idref="oc-190"/>
      </symbol>
      <symbol id="sm-41e">
         <name>_HOSTread</name>
         <value>0x564e</value>
         <object_component_ref idref="oc-192"/>
      </symbol>
      <symbol id="sm-42a">
         <name>_HOSTrename</name>
         <value>0x568d</value>
         <object_component_ref idref="oc-196"/>
      </symbol>
      <symbol id="sm-436">
         <name>_HOSTunlink</name>
         <value>0x57f5</value>
         <object_component_ref idref="oc-195"/>
      </symbol>
      <symbol id="sm-443">
         <name>_HOSTwrite</name>
         <value>0x56cb</value>
         <object_component_ref idref="oc-193"/>
      </symbol>
      <symbol id="sm-45c">
         <name>___TI_readmsg</name>
         <value>0x540c</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-45d">
         <name>C$$IO$$</name>
         <value>0x5407</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-45e">
         <name>__CIOBUF_</name>
         <value>0x400</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-45f">
         <name>___TI_writemsg</name>
         <value>0x53b4</value>
         <object_component_ref idref="oc-19c"/>
      </symbol>
      <symbol id="sm-473">
         <name>__sys_memory</name>
         <value>0x9000</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
