/**
 * @file stateMachine.h
 * @brief State machine header for pitch control system
 * @note Compliant with MISRA-C:2004 standard
 */

#ifndef STATE_MACHINE_H
#define STATE_MACHINE_H

/* Basic type definitions - MISRA-C compliant */
typedef unsigned long  u32;
typedef unsigned short u16;
typedef unsigned char  u8;
typedef unsigned char  bool;

#define TRUE  ((bool)1U)
#define FALSE ((bool)0U)

/* State enumeration - Big CamelCase naming */
typedef enum {
    POWER_ON = 1U,           /* Power on state */
    STANDBY = 3U,            /* Standby state */
    NORMAL = 4U,             /* Normal operation state */
    EMERGENCY = 5U,          /* Emergency state */
    PRE_NORMAL = 6U,         /* Pre-normal state */
    BACKUP_TEST = 7U,        /* Backup power test state */
    MANUAL = 8U,             /* Manual state */
    VORTEX_RUN = 9U,         /* Vortex run mode */
    VORTEX_BRAKE = 10U,      /* Vortex brake mode */
    STATE_MAX = 11U          /* Total number of states */
} SystemState;

/* Transition condition enumeration */
typedef enum {
    TF1_8 = 0U,   /* PowerOn->Manual */
    TF1_3 = 1U,   /* PowerOn->Standby */
    TF3_6 = 2U,   /* Standby->PreNormal */
    TF6_4 = 3U,   /* PreNormal->Normal */
    TF6_5 = 4U,   /* PreNormal->VortexRun */
    TF9_10 = 5U,  /* VortexRun->VortexBrake */
    TF10_5 = 6U,  /* VortexBrake->Emergency */
    TF4_5 = 7U,   /* Normal->Emergency */
    TF5_7 = 8U,   /* Emergency->BackupTest */
    TF7_3 = 9U,   /* BackupTest->Standby */
    TF8_3 = 10U,  /* Manual->Standby */
    TF5_3 = 11U,  /* Emergency->Standby */
    TF9_5 = 12U,  /* VortexRun->Emergency */
    TF_MAX = 13U  /* Maximum transition conditions */
} TransitionCondition;

/* Global transition condition variables - with 'g' prefix */
extern u16 gTf18Condition;    /* PowerOn->Manual condition */
extern u16 gTf13Condition;    /* PowerOn->Standby condition */
extern u16 gTf36Condition;    /* Standby->PreNormal condition */
extern u16 gTf64Condition;    /* PreNormal->Normal condition */
extern u16 gTf65Condition;    /* PreNormal->VortexRun condition */
extern u16 gTf910Condition;   /* VortexRun->VortexBrake condition */
extern u16 gTf105Condition;   /* VortexBrake->Emergency condition */
extern u16 gTf45Condition;    /* Normal->Emergency condition */
extern u16 gTf57Condition;    /* Emergency->BackupTest condition */
extern u16 gTf73Condition;    /* BackupTest->Standby condition */
extern u16 gTf83Condition;    /* Manual->Standby condition */
extern u16 gTf53Condition;    /* Emergency->Standby condition */
extern u16 gTf95Condition;    /* VortexRun->Emergency condition */

/* Bit manipulation functions - MISRA-C compliant */
extern u16 setBit(u16 num, u8 bitPos);
extern u16 clrBit(u16 num, u8 bitPos);

/* Global state action counters - with 'g' prefix */
extern u32 gPowerOnCnt;       /* Power on state counter */
extern u32 gStandbyCnt;       /* Standby state counter */
extern u32 gPreNormalCnt;     /* Pre-normal state counter */
extern u32 gNormalCnt;        /* Normal state counter */
extern u32 gEmergencyCnt;     /* Emergency state counter */
extern u32 gBackupTestCnt;    /* Backup test state counter */
extern u32 gManualCnt;        /* Manual state counter */
extern u32 gVortexRunCnt;     /* Vortex run state counter */
extern u32 gVortexBrakeCnt;   /* Vortex brake state counter */

/* Global current state variable - with dual protection */
extern SystemState gCurrentState;
extern SystemState gCurrentStateBak;  /* Backup for critical protection */

/* Function prototypes */
SystemState getCurrentState(void);
void stateMachineProcess(void);

#endif /* STATE_MACHINE_H */
